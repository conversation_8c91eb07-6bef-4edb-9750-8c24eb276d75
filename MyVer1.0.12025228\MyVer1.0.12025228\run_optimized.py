# -*- coding: utf-8 -*-
"""
优化版本启动脚本
确保所有依赖正确加载并启动应用程序
"""

import sys
import os
import logging

def check_dependencies():
    """检查依赖是否安装"""
    required_modules = [
        'customtkinter',
        'cv2',
        'numpy',
        'pyautogui',
        'keyboard',
        'PIL',
        'pynput'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("缺少以下依赖模块:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('app.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def main():
    """主函数"""
    print("=" * 60)
    print("魔兽世界技能助手 - 优化版本")
    print("=" * 60)
    
    # 检查依赖
    print("检查依赖模块...")
    if not check_dependencies():
        input("按回车键退出...")
        return False
    
    print("✓ 所有依赖模块已安装")
    
    # 设置日志
    setup_logging()
    
    # 启动应用程序
    try:
        print("启动应用程序...")
        from XXD import WoWSkillAssistant
        
        app = WoWSkillAssistant()
        print("✓ 应用程序初始化成功")
        print("✓ 启动完成，开始运行...")
        
        app.run()
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return True
    except Exception as e:
        print(f"启动失败: {e}")
        logging.error(f"应用程序启动失败: {e}", exc_info=True)
        input("按回车键退出...")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
