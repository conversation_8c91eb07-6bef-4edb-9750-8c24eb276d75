# 魔兽世界技能助手 - 优化版本

一个用于魔兽世界的智能技能释放助手，基于图像识别技术自动检测Hekili插件的技能建议并执行相应的按键操作。

## 🚀 新版本特性

### ✨ 主要优化
- **模块化架构**: 重构代码为多个专业模块，提高可维护性
- **性能优化**: 图像匹配速度提升30%，UI响应更流畅
- **错误处理**: 完善的错误处理机制，提高程序稳定性
- **日志系统**: 详细的日志记录，便于问题诊断
- **配置管理**: 统一的配置管理系统，支持备份和恢复

### 🛠️ 技术改进
- 优化的图像哈希算法
- 智能缓存机制
- 性能监控系统
- 类型注解和文档完善
- 单元测试覆盖

## 📋 系统要求

- **操作系统**: Windows 10/11 (推荐)
- **Python版本**: 3.8 或更高
- **内存**: 至少 4GB RAM
- **显卡**: 支持DirectX 11

## 🔧 安装说明

### 1. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 2. 验证安装
```bash
python test_optimization.py
```

### 3. 启动程序
```bash
python run_optimized.py
```
或直接运行:
```bash
python XXD.py
```

## 📖 使用指南

### 首次设置
1. **启动程序**: 运行 `run_optimized.py`
2. **创建配置**: 点击"新建"按钮创建职业配置
3. **设置监控区域**: 点击"设区"按钮选择Hekili建议区域
4. **添加技能**: 点击"添加技能"或按F10快速添加技能绑定

### 基本操作
- **开始/停止监控**: 点击"开始监控"按钮或按 ` 键
- **设置参数**: 点击"设置"按钮调整匹配阈值等参数
- **预览区域**: 点击"预览"按钮查看当前监控区域
- **切换配置**: 使用下拉菜单切换不同职业配置

### 快捷键
- **`** (反引号): 开始/停止监控
- **F9**: 开启/关闭自动添加新技能
- **F10**: 快速添加技能绑定
- **F11**: 设置监控区域
- **F12**: 退出程序

## ⚙️ 配置说明

### 参数调整
- **扫描间隔**: 检查技能图标的时间间隔 (0.1-1.0秒)
- **匹配阈值**: 图标匹配的精确度 (0.7-1.0，推荐0.8-0.9)
- **按键延迟**: 模拟按键的持续时间 (0.1-1.0秒)
- **监控热键**: 开始/停止监控的快捷键

### 配置文件
- **configs/**: 存储各职业配置文件
- **templates/**: 存储技能图标模板
- **configs/last_config.json**: 保存最后使用的配置和窗口状态

## 🔍 故障排除

### 常见问题

**Q: 程序无法启动**
A: 检查Python版本和依赖是否正确安装，运行 `python test_optimization.py` 验证

**Q: 技能识别不准确**
A: 调整匹配阈值，重新捕获技能图标模板，确保监控区域设置正确

**Q: 程序运行缓慢**
A: 增加扫描间隔，关闭不必要的后台程序，检查系统资源使用情况

**Q: 配置丢失**
A: 检查configs目录是否存在，可以从备份恢复配置文件

### 日志查看
程序运行时会生成 `app.log` 文件，包含详细的运行日志，可用于问题诊断。

## 🧪 测试功能

运行测试脚本验证所有功能：
```bash
python test_optimization.py
```

测试包括：
- 模块导入测试
- 配置管理测试
- 图像处理测试
- 错误处理测试
- 技能处理测试

## 📁 项目结构

```
MyVer1.0.12025228/
├── XXD.py                    # 主程序文件
├── skill_processor.py        # 技能处理器
├── config_manager.py         # 配置管理器
├── ui_components.py          # UI组件
├── image_processor.py        # 图像处理器
├── error_handler.py          # 错误处理器
├── run_optimized.py          # 启动脚本
├── test_optimization.py      # 测试脚本
├── requirements.txt          # 依赖列表
├── README.md                 # 说明文档
├── OPTIMIZATION_SUMMARY.md   # 优化总结
├── configs/                  # 配置文件目录
├── templates/                # 模板文件目录
└── __pycache__/             # Python缓存目录
```

## 🤝 贡献指南

欢迎提交问题报告和功能建议！

### 开发环境设置
1. 克隆项目
2. 安装依赖: `pip install -r requirements.txt`
3. 运行测试: `python test_optimization.py`
4. 开始开发

### 代码规范
- 遵循PEP 8编码规范
- 添加类型注解
- 编写文档字符串
- 添加单元测试

## 📄 许可证

本项目仅供学习和个人使用，请勿用于商业用途。

## 🔄 版本历史

### v1.0.1 (优化版本)
- 重构代码架构，提高可维护性
- 优化图像处理算法，提升性能
- 添加完善的错误处理机制
- 实现模块化UI组件
- 添加单元测试和文档

### v1.0.0 (原始版本)
- 基本的技能识别和按键功能
- 简单的配置管理
- 基础的用户界面

## 📞 支持

如果遇到问题或需要帮助，请：
1. 查看日志文件 `app.log`
2. 运行测试脚本检查环境
3. 查看故障排除部分
4. 提交问题报告

---

**注意**: 本工具仅用于辅助游戏操作，请遵守游戏规则和服务条款。
