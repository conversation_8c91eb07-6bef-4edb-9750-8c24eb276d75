# -*- coding: utf-8 -*-
"""
简化的应用启动脚本
避免编码问题，直接启动优化后的程序
"""

import sys
import os

def main():
    """主函数"""
    print("=" * 50)
    print("魔兽世界技能助手 - 优化版本")
    print("=" * 50)
    
    try:
        print("正在启动程序...")
        
        # 导入并启动主程序
        from XXD import WoWSkillAssistant
        
        print("程序初始化中...")
        app = WoWSkillAssistant()
        
        print("启动成功！")
        print("=" * 50)
        print("优化特性:")
        print("- 高效的增量UI更新系统")
        print("- 保持原有的双击菜单操作")
        print("- 添加/删除技能即时响应")
        print("- 支持大量技能绑定")
        print("- 完善的错误处理机制")
        print("=" * 50)
        
        # 运行程序
        app.run()
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return True
    except Exception as e:
        print(f"启动失败: {e}")
        print("\n请检查:")
        print("1. Python依赖是否正确安装")
        print("2. 配置文件是否完整")
        print("3. 查看app.log日志文件")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("按回车键退出...")
    sys.exit(0 if success else 1)
