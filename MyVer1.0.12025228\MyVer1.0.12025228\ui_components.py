# -*- coding: utf-8 -*-
"""
UI组件模块
包含各种自定义UI组件和对话框
"""

import tkinter as tk
import customtkinter as ctk
from typing import Callable, Optional, Any
import logging

logger = logging.getLogger(__name__)

class StatusBar(ctk.CTkFrame):
    """状态栏组件"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.status_label = ctk.CTkLabel(
            self, 
            text="就绪",
            font=("Arial", 12)
        )
        self.status_label.pack(side="left", padx=5)
        
        # 添加进度指示器（可选）
        self.progress_var = ctk.StringVar(value="")
        self.progress_label = ctk.CTkLabel(
            self,
            textvariable=self.progress_var,
            font=("Arial", 10)
        )
        self.progress_label.pack(side="right", padx=5)
    
    def set_status(self, text: str):
        """设置状态文本"""
        self.status_label.configure(text=text)
    
    def set_progress(self, text: str):
        """设置进度文本"""
        self.progress_var.set(text)

class ControlPanel(ctk.CTkFrame):
    """控制面板组件"""
    
    def __init__(self, parent, callbacks: dict, **kwargs):
        super().__init__(parent, **kwargs)
        self.callbacks = callbacks
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        # 左侧按钮组
        left_frame = ctk.CTkFrame(self)
        left_frame.pack(side="left", fill="x", expand=True)
        
        self.start_btn = ctk.CTkButton(
            left_frame,
            text="开始监控",
            command=self.callbacks.get('toggle_monitoring'),
            width=120,
            height=28
        )
        self.start_btn.pack(side="left", padx=2)
        
        self.settings_btn = ctk.CTkButton(
            left_frame,
            text="设置",
            command=self.callbacks.get('show_settings'),
            width=60,
            height=28
        )
        self.settings_btn.pack(side="left", padx=2)
        
        # 区域信息标签
        self.region_info_label = ctk.CTkLabel(
            left_frame,
            text="未设置监控区域",
            font=("Arial", 12)
        )
        self.region_info_label.pack(side="left", padx=5)
        
        # 右侧按钮组
        right_frame = ctk.CTkFrame(self)
        right_frame.pack(side="right", fill="x")
        
        self.set_region_btn = ctk.CTkButton(
            right_frame,
            text="设区",
            command=self.callbacks.get('set_monitor_region'),
            width=60,
            height=28
        )
        self.set_region_btn.pack(side="left", padx=2)
        
        self.preview_region_btn = ctk.CTkButton(
            right_frame,
            text="预览",
            command=self.callbacks.get('preview_monitor_region'),
            width=60,
            height=28
        )
        self.preview_region_btn.pack(side="left", padx=2)
        
        self.add_binding_btn = ctk.CTkButton(
            right_frame,
            text="添加技能",
            command=self.callbacks.get('quick_add_binding'),
            width=80,
            height=28
        )
        self.add_binding_btn.pack(side="left", padx=2)
    
    def update_start_button(self, is_running: bool):
        """更新开始按钮状态"""
        if is_running:
            self.start_btn.configure(
                text="停止监控 (~)",
                fg_color="#D35B58"
            )
        else:
            self.start_btn.configure(
                text="开始监控 (~)",
                fg_color="#3B8ED0"
            )
    
    def update_region_info(self, region_text: str):
        """更新区域信息"""
        self.region_info_label.configure(text=region_text)

class SpecSelector(ctk.CTkFrame):
    """职业选择器组件"""
    
    def __init__(self, parent, specs: list, current_spec: str, callbacks: dict, **kwargs):
        super().__init__(parent, **kwargs)
        self.callbacks = callbacks
        self._setup_ui(specs, current_spec)
    
    def _setup_ui(self, specs: list, current_spec: str):
        """设置UI"""
        # 职业选择下拉框
        self.spec_var = ctk.StringVar(value=current_spec if current_spec else "请创建配置")
        
        self.spec_dropdown = ctk.CTkOptionMenu(
            self,
            variable=self.spec_var,
            values=specs if specs else ["请创建配置"],
            command=self.callbacks.get('on_spec_change'),
            width=100
        )
        self.spec_dropdown.pack(side="left", padx=2)
        
        # 新建按钮
        self.add_spec_btn = ctk.CTkButton(
            self,
            text="新建",
            command=self.callbacks.get('create_new_spec'),
            width=60,
            height=24
        )
        self.add_spec_btn.pack(side="left", padx=2)
        
        # 删除按钮
        self.delete_spec_btn = ctk.CTkButton(
            self,
            text="删除",
            command=self.callbacks.get('delete_spec'),
            width=60,
            height=24,
            fg_color="#D35B58",
            hover_color="#C15856"
        )
        self.delete_spec_btn.pack(side="left", padx=2)
    
    def update_specs(self, specs: list, current_spec: str):
        """更新职业列表"""
        self.spec_dropdown.configure(values=specs if specs else ["请创建配置"])
        self.spec_var.set(current_spec if current_spec else "请创建配置")

class SettingsDialog(ctk.CTkToplevel):
    """设置对话框"""
    
    def __init__(self, parent, settings: dict, save_callback: Callable):
        super().__init__(parent)
        self.settings = settings.copy()
        self.save_callback = save_callback
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        self.title("设置")
        self.geometry("350x400")
        self.attributes('-topmost', True)
        self.resizable(False, False)
        
        # 居中显示
        self._center_window()
        
        frame = ctk.CTkFrame(self)
        frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 扫描间隔设置
        self._create_setting_row(frame, "扫描间隔(秒):", "scan_interval")
        
        # 匹配阈值设置
        self._create_setting_row(frame, "匹配阈值(0-1):", "threshold")
        
        # 按键延迟设置
        self._create_setting_row(frame, "按键延迟(秒):", "key_press_delay")
        
        # 监控热键设置
        self._create_setting_row(frame, "监控热键:", "monitor_hotkey")
        
        # 自动添加技能开关
        auto_add_frame = ctk.CTkFrame(frame)
        auto_add_frame.pack(fill="x", padx=5, pady=5)
        
        self.auto_add_var = ctk.BooleanVar(value=self.settings.get('auto_add_skills', True))
        auto_add_switch = ctk.CTkSwitch(
            auto_add_frame, 
            text="自动添加新技能",
            variable=self.auto_add_var
        )
        auto_add_switch.pack(side="left", padx=5)
        
        # 保存按钮
        save_btn = ctk.CTkButton(frame, text="保存", command=self._save_settings)
        save_btn.pack(pady=10)
        
        # 帮助文本
        help_text = """参数说明:
- 扫描间隔: 检查技能图标的时间间隔
- 匹配阈值: 图标匹配的精确度,建议0.8-0.9
- 按键延迟: 模拟按键的持续时间
- 监控热键: 开始/停止监控的快捷键

快捷键:
- F9: 开启/关闭自动添加技能
- F10: 快速添加技能
- F11: 设置监控区域
- F12: 退出程序"""
        
        help_label = ctk.CTkLabel(frame, text=help_text, justify="left")
        help_label.pack(pady=10)
    
    def _create_setting_row(self, parent, label_text: str, setting_key: str):
        """创建设置行"""
        frame = ctk.CTkFrame(parent)
        frame.pack(fill="x", padx=5, pady=5)
        
        ctk.CTkLabel(frame, text=label_text).pack(side="left", padx=5)
        
        var = ctk.StringVar(value=str(self.settings[setting_key]))
        entry = ctk.CTkEntry(frame, textvariable=var, width=100)
        entry.pack(side="left", padx=5)
        
        # 保存变量引用
        setattr(self, f"{setting_key}_var", var)
    
    def _center_window(self):
        """窗口居中"""
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        window_width = 350
        window_height = 400
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def _save_settings(self):
        """保存设置"""
        try:
            new_settings = {
                'scan_interval': float(self.scan_interval_var.get()),
                'threshold': float(self.threshold_var.get()),
                'key_press_delay': float(self.key_press_delay_var.get()),
                'monitor_hotkey': self.monitor_hotkey_var.get(),
                'auto_add_skills': self.auto_add_var.get()
            }
            
            # 验证输入值
            self._validate_settings(new_settings)
            
            # 调用保存回调
            if self.save_callback(new_settings):
                self.destroy()
            
        except ValueError as e:
            self._show_error(f"设置错误: {str(e)}")
        except Exception as e:
            self._show_error(f"保存设置时出错: {str(e)}")
    
    def _validate_settings(self, settings: dict):
        """验证设置"""
        if not (0 < settings['scan_interval'] <= 1):
            raise ValueError("扫描间隔必须在0-1秒之间")
        if not (0 < settings['threshold'] <= 1):
            raise ValueError("匹配阈值必须在0-1之间")
        if not (0 < settings['key_press_delay'] <= 1):
            raise ValueError("按键延迟必须在0-1秒之间")
        if not settings['monitor_hotkey']:
            raise ValueError("监控热键不能为空")
    
    def _show_error(self, message: str):
        """显示错误消息"""
        error_dialog = ctk.CTkToplevel(self)
        error_dialog.title("错误")
        error_dialog.geometry("300x100")
        error_dialog.attributes('-topmost', True)
        
        ctk.CTkLabel(error_dialog, text=message).pack(pady=20)
        ctk.CTkButton(error_dialog, text="确定", command=error_dialog.destroy).pack()

class ConfirmDialog(ctk.CTkToplevel):
    """确认对话框"""
    
    def __init__(self, parent, title: str, message: str, callback: Callable[[bool], None]):
        super().__init__(parent)
        self.callback = callback
        self.result = False
        self._setup_ui(title, message)
    
    def _setup_ui(self, title: str, message: str):
        """设置UI"""
        self.title(title)
        self.geometry("300x150")
        self.attributes('-topmost', True)
        self.resizable(False, False)
        
        # 居中显示
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        x = (screen_width - 300) // 2
        y = (screen_height - 150) // 2
        self.geometry(f"300x150+{x}+{y}")
        
        # 消息标签
        ctk.CTkLabel(self, text=message, wraplength=250).pack(pady=20)
        
        # 按钮框架
        button_frame = ctk.CTkFrame(self)
        button_frame.pack(pady=10)
        
        # 确定按钮
        ctk.CTkButton(
            button_frame,
            text="确定",
            command=self._on_confirm,
            width=80
        ).pack(side="left", padx=10)
        
        # 取消按钮
        ctk.CTkButton(
            button_frame,
            text="取消",
            command=self._on_cancel,
            width=80
        ).pack(side="left", padx=10)
        
        # 绑定关闭事件
        self.protocol("WM_DELETE_WINDOW", self._on_cancel)
    
    def _on_confirm(self):
        """确认按钮回调"""
        self.result = True
        self.destroy()
        if self.callback:
            self.callback(True)
    
    def _on_cancel(self):
        """取消按钮回调"""
        self.result = False
        self.destroy()
        if self.callback:
            self.callback(False)
