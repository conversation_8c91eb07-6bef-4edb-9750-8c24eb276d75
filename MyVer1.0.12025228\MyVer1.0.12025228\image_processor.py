# -*- coding: utf-8 -*-
"""
图像处理优化模块
提供高效的图像匹配和处理功能
"""

import cv2
import numpy as np
import pyautogui
from typing import Tuple, Optional
import logging
from concurrent.futures import ThreadPoolExecutor
import threading

logger = logging.getLogger(__name__)

class ImageProcessor:
    """优化的图像处理器"""
    
    def __init__(self, max_workers: int = 2):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self._cache = {}
        self._cache_lock = threading.Lock()
        self.max_cache_size = 100
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
    
    def calculate_image_hash(self, image: np.ndarray, hash_size: int = 16) -> Tuple[np.ndarray, str]:
        """
        计算图像的感知哈希值
        
        Args:
            image: 输入图像
            hash_size: 哈希大小
            
        Returns:
            (hash_array, hash_string): 哈希数组和字符串
        """
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 缩放到指定大小
            resized = cv2.resize(gray, (hash_size + 1, hash_size), interpolation=cv2.INTER_AREA)
            
            # 计算差值
            diff = resized[:, 1:] > resized[:, :-1]
            
            # 转换为二进制字符串
            hash_str = ''.join(['1' if b else '0' for b in diff.flatten()])
            
            return diff, hash_str
            
        except Exception as e:
            logger.error(f"计算图像哈希时出错: {e}")
            return np.array([]), ""
    
    def calculate_hash_similarity(self, hash1: np.ndarray, hash2: np.ndarray) -> Tuple[float, int]:
        """
        计算两个哈希值的相似度和汉明距离
        
        Args:
            hash1: 第一个哈希
            hash2: 第二个哈希
            
        Returns:
            (similarity, hamming_distance): 相似度和汉明距离
        """
        try:
            if hash1.shape != hash2.shape:
                return 0.0, hash1.size if hash1.size > 0 else 1000
            
            # 计算汉明距离
            hamming_distance = np.sum(hash1 != hash2)
            max_distance = hash1.size
            
            # 转换为相似度
            similarity = 1 - (hamming_distance / max_distance) if max_distance > 0 else 0.0
            
            return similarity, hamming_distance
            
        except Exception as e:
            logger.error(f"计算哈希相似度时出错: {e}")
            return 0.0, 1000
    
    def find_template_in_image(self, template: np.ndarray, image: np.ndarray, 
                              threshold: float = 0.8) -> Tuple[float, int]:
        """
        在图像中查找模板并返回最佳匹配
        
        Args:
            template: 模板图像
            image: 搜索图像
            threshold: 匹配阈值
            
        Returns:
            (max_similarity, min_hamming): 最大相似度和最小汉明距离
        """
        try:
            # 检查输入
            if template.size == 0 or image.size == 0:
                return 0.0, 1000
            
            # 转换为灰度图
            template_gray = self._to_grayscale(template)
            image_gray = self._to_grayscale(image)
            
            # 获取模板哈希
            template_hash, _ = self.calculate_image_hash(template_gray)
            if template_hash.size == 0:
                return 0.0, 1000
            
            # 获取图像尺寸
            img_h, img_w = image_gray.shape[:2]
            tmpl_h, tmpl_w = template_gray.shape[:2]
            
            # 检查模板是否比图像大
            if tmpl_h > img_h or tmpl_w > img_w:
                return 0.0, 1000
            
            max_similarity = 0.0
            min_hamming = template_hash.size
            
            # 使用步长优化搜索
            step_size = max(1, min(tmpl_h, tmpl_w) // 4)
            
            # 在图像中滑动窗口
            for y in range(0, img_h - tmpl_h + 1, step_size):
                for x in range(0, img_w - tmpl_w + 1, step_size):
                    # 提取当前窗口
                    window = image_gray[y:y+tmpl_h, x:x+tmpl_w]
                    
                    # 计算窗口哈希
                    window_hash, _ = self.calculate_image_hash(window)
                    if window_hash.size == 0:
                        continue
                    
                    # 计算相似度
                    similarity, hamming = self.calculate_hash_similarity(template_hash, window_hash)
                    
                    if similarity > max_similarity:
                        max_similarity = similarity
                        min_hamming = hamming
                    
                    # 如果找到足够好的匹配，可以提前返回
                    if similarity >= threshold:
                        return similarity, hamming
            
            # 如果步长搜索找到了不错的匹配，进行精细搜索
            if max_similarity > threshold * 0.8:
                max_similarity, min_hamming = self._fine_search(
                    template_gray, image_gray, template_hash, threshold
                )
            
            return max_similarity, min_hamming
            
        except Exception as e:
            logger.error(f"模板匹配时出错: {e}")
            return 0.0, 1000
    
    def _fine_search(self, template: np.ndarray, image: np.ndarray, 
                    template_hash: np.ndarray, threshold: float) -> Tuple[float, int]:
        """
        精细搜索，用于在粗搜索基础上提高精度
        """
        try:
            img_h, img_w = image.shape[:2]
            tmpl_h, tmpl_w = template.shape[:2]
            
            max_similarity = 0.0
            min_hamming = template_hash.size
            
            # 逐像素搜索
            for y in range(0, img_h - tmpl_h + 1):
                for x in range(0, img_w - tmpl_w + 1):
                    window = image[y:y+tmpl_h, x:x+tmpl_w]
                    window_hash, _ = self.calculate_image_hash(window)
                    
                    if window_hash.size == 0:
                        continue
                    
                    similarity, hamming = self.calculate_hash_similarity(template_hash, window_hash)
                    
                    if similarity > max_similarity:
                        max_similarity = similarity
                        min_hamming = hamming
                    
                    if similarity >= threshold:
                        return similarity, hamming
            
            return max_similarity, min_hamming
            
        except Exception as e:
            logger.error(f"精细搜索时出错: {e}")
            return 0.0, 1000
    
    def _to_grayscale(self, image: np.ndarray) -> np.ndarray:
        """转换为灰度图"""
        if len(image.shape) == 3:
            return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        return image.copy()
    
    def capture_screen_region(self, region: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """
        捕获屏幕区域
        
        Args:
            region: (x, y, width, height)
            
        Returns:
            捕获的图像或None
        """
        try:
            x, y, w, h = region
            screenshot = pyautogui.screenshot(region=(x, y, w, h))
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            logger.error(f"捕获屏幕区域时出错: {e}")
            return None
    
    def preprocess_template(self, template: np.ndarray) -> np.ndarray:
        """
        预处理模板图像以提高匹配精度
        
        Args:
            template: 原始模板
            
        Returns:
            处理后的模板
        """
        try:
            # 转换为灰度图
            if len(template.shape) == 3:
                gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            else:
                gray = template.copy()
            
            # 应用高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)
            
            # 增强对比度
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(blurred)
            
            return enhanced
            
        except Exception as e:
            logger.error(f"预处理模板时出错: {e}")
            return template
    
    def get_template_cache_key(self, template: np.ndarray) -> str:
        """生成模板缓存键"""
        try:
            # 使用模板的哈希作为缓存键
            _, hash_str = self.calculate_image_hash(template)
            return hash_str
        except:
            return ""
    
    def cache_template_hash(self, template: np.ndarray) -> Optional[np.ndarray]:
        """缓存模板哈希"""
        try:
            cache_key = self.get_template_cache_key(template)
            if not cache_key:
                return None
            
            with self._cache_lock:
                if cache_key in self._cache:
                    return self._cache[cache_key]
                
                # 计算并缓存哈希
                template_hash, _ = self.calculate_image_hash(template)
                
                # 限制缓存大小
                if len(self._cache) >= self.max_cache_size:
                    # 移除最旧的条目
                    oldest_key = next(iter(self._cache))
                    del self._cache[oldest_key]
                
                self._cache[cache_key] = template_hash
                return template_hash
                
        except Exception as e:
            logger.error(f"缓存模板哈希时出错: {e}")
            return None
    
    def clear_cache(self):
        """清空缓存"""
        with self._cache_lock:
            self._cache.clear()
    
    def validate_image(self, image: np.ndarray) -> bool:
        """验证图像是否有效"""
        try:
            if image is None or image.size == 0:
                return False
            
            # 检查图像尺寸
            if len(image.shape) not in [2, 3]:
                return False
            
            # 检查图像是否太小
            h, w = image.shape[:2]
            if h < 5 or w < 5:
                return False
            
            return True
            
        except Exception:
            return False
