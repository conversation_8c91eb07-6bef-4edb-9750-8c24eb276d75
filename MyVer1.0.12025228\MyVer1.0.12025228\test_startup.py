# -*- coding: utf-8 -*-
"""
启动测试脚本
测试优化后的程序是否能正常启动和运行
"""

import sys
import os
import time

def test_startup():
    """测试程序启动"""
    print("测试程序启动...")
    
    try:
        # 导入主程序
        from XXD import WoWSkillAssistant
        print("✓ 主程序导入成功")
        
        # 创建应用实例（但不运行GUI）
        print("创建应用实例...")
        app = WoWSkillAssistant()
        print("✓ 应用实例创建成功")
        
        # 检查关键属性
        if hasattr(app, 'processor'):
            print("✓ 技能处理器已初始化")
        else:
            print("✗ 技能处理器未初始化")
            return False
        
        if hasattr(app, 'config_manager'):
            print("✓ 配置管理器已初始化")
        else:
            print("✗ 配置管理器未初始化")
            return False
        
        if hasattr(app, 'root'):
            print("✓ 主窗口已创建")
        else:
            print("✗ 主窗口未创建")
            return False
        
        # 测试配置加载
        if app.current_spec:
            print(f"✓ 当前配置: {app.current_spec}")
        else:
            print("ℹ 没有当前配置（这是正常的）")
        
        print(f"✓ 找到 {len(app.specs)} 个配置文件")
        
        # 测试基本功能
        if hasattr(app, 'update_binding_list'):
            print("✓ UI更新方法可用")
        
        if hasattr(app, 'toggle_monitoring'):
            print("✓ 监控切换方法可用")
        
        print("✓ 程序启动测试成功！")
        return True
        
    except Exception as e:
        print(f"✗ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始启动测试...")
    print("=" * 40)
    
    success = test_startup()
    
    print("=" * 40)
    if success:
        print("🎉 启动测试通过！程序可以正常启动。")
        print("\n提示：")
        print("- 程序已经过优化，性能和稳定性得到提升")
        print("- 可以使用 python XXD.py 启动完整程序")
        print("- 可以使用 python run_optimized.py 启动带检查的版本")
    else:
        print("❌ 启动测试失败，需要修复问题")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
