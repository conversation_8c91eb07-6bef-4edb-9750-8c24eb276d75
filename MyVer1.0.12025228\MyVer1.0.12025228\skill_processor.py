# -*- coding: utf-8 -*-
"""
技能处理器模块
负责技能图标识别和按键模拟
"""

import cv2
import numpy as np
import pyautogui
import keyboard
import time
import json
import os
import threading
import logging
from dataclasses import dataclass, asdict
from typing import Dict, Optional, Tuple, List
from queue import Queue

from image_processor import ImageProcessor

# 设置日志
logger = logging.getLogger(__name__)

@dataclass
class IconBinding:
    """技能图标与按键绑定"""
    name: str        # 内部名称，用于文件命名
    hotkey: str
    template: np.ndarray
    text: str = ""   # 显示名称，默认为空，后面会根据name生成
    threshold: float = 0.8
    last_cast: float = 0.0
    cooldown: float = 0.5
    match_count: int = 0
    total_similarity: float = 0.0
    max_similarity: float = 0.0
    min_similarity: float = 1.0

    def __post_init__(self):
        """在初始化后设置默认的text值"""
        if not self.text:
            # 如果没有提供text，使用name中的数字部分
            import re
            number = re.search(r'\d+', self.name)
            if number:
                self.text = f"S-{number.group()}"
            else:
                self.text = self.name

    def update_stats(self, similarity: float):
        """更新匹配度统计"""
        self.match_count += 1
        self.total_similarity += similarity
        self.max_similarity = max(self.max_similarity, similarity)
        self.min_similarity = min(self.min_similarity, similarity)

    def get_avg_similarity(self) -> float:
        """获取平均匹配度"""
        return self.total_similarity / self.match_count if self.match_count > 0 else 0.0

    def get_stats_str(self) -> str:
        """获取统计信息字符串"""
        if self.match_count == 0:
            return " "  # 使用空格
        return f"均{self.get_avg_similarity():.0%}"  # 只显示平均匹配度

    def to_dict(self):
        """转换为可序列化的字典"""
        data = asdict(self)
        # 移除不需要保存的字段
        data.pop('template')
        data.pop('match_count')
        data.pop('total_similarity')
        data.pop('max_similarity')
        data.pop('min_similarity')
        return data

    @classmethod
    def from_dict(cls, data: dict, template: np.ndarray):
        """从字典和模板图像创建实例"""
        return cls(
            name=data['name'],
            hotkey=data['hotkey'],
            template=template,
            text=data.get('text', ''),  # 如果没有text，会通过__post_init__设置默认值
            threshold=data.get('threshold', 0.8),
            last_cast=0.0,
            cooldown=data.get('cooldown', 0.5)
        )

class HekiliProcessor:
    """技能处理器主类"""

    def __init__(self):
        self.monitor_region = None
        self.icon_bindings: Dict[str, IconBinding] = {}
        self.enabled = False

        # 使用绝对路径保存配置和图标
        self.base_dir = os.path.abspath(os.path.dirname(__file__))

        self.lock = threading.Lock()
        self.event_queue = Queue()
        self.status_callback = None
        self.last_status = ""

        # 初始化图像处理器
        self.image_processor = ImageProcessor()

        # 修改默认设置值，添加 auto_add_skills 默认为 True
        self.settings = {
            'monitor_hotkey': '`',      # 使用 ` 代表 ~ 键
            'threshold': 0.90,          # 匹配阈值改为0.90
            'scan_interval': 0.33,      # 扫描间隔改为0.33秒
            'key_press_delay': 0.19,    # 按键延迟改为0.19秒
            'auto_add_skills': True     # 自动添加技能默认开启
        }

        # 配置目录
        self.config_dir = "configs"

        # 确保配置目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs("templates", exist_ok=True)

        self.last_match_value = 0.0

        logger.info("HekiliProcessor 初始化完成")
        
    def get_icon_path(self, name: str, spec_name: str) -> str:
        """获取图标文件路径"""
        # 直接使用 templates 目录和配置名称
        safe_name = "".join(c for c in name if c.isalnum() or c == '_')
        return os.path.join("templates", f"{spec_name}_{safe_name}.png")
        
    def save_icon_template(self, name: str, template: np.ndarray) -> bool:
        """保存图标模板到文件"""
        try:
            icon_path = self.get_icon_path(name)
            success = cv2.imwrite(icon_path, template)
            if success:
                print(f"保存图标模板成功: {icon_path}")
                return True
            else:
                print(f"保存图标模板失败: {icon_path}")
                return False
        except Exception as e:
            print(f"保存图标模板时出错: {str(e)}")
            return False
        
    def load_icon_template(self, name: str) -> Optional[np.ndarray]:
        """从文件加载图标模板"""
        try:
            icon_path = self.get_icon_path(name)
            if os.path.exists(icon_path):
                template = cv2.imread(icon_path)
                if template is not None:
                    binding = self.icon_bindings.get(name)
                    text = binding.text if binding else name
                    print(f"加载图标模板: {text}")  # 使用text
                    return template
                else:
                    print(f"图标模板读取失败: {icon_path}")
            else:
                print(f"图标模板不存在: {icon_path}")
        except Exception as e:
            print(f"加载图标模板失败: {str(e)}")
        return None
        
    def add_icon_binding(self, name: str, text: str, hotkey: str, template_image: np.ndarray) -> Optional[IconBinding]:
        """
        添加技能图标与按键的绑定

        Args:
            name: 内部名称
            text: 显示名称
            hotkey: 快捷键
            template_image: 模板图像

        Returns:
            创建的绑定对象或None
        """
        with self.lock:
            try:
                # 验证输入
                if not name or not hotkey:
                    logger.error("名称和快捷键不能为空")
                    return None

                if not self.image_processor.validate_image(template_image):
                    logger.error("模板图像无效")
                    return None

                # 确保内部名称唯一
                base_name = name
                counter = 1
                while name in self.icon_bindings:
                    name = f"{base_name}_{counter}"
                    counter += 1

                # 创建绑定
                binding = IconBinding(
                    name=name,      # 内部名称
                    text=text or name,      # 显示名称，如果为空则使用内部名称
                    hotkey=hotkey,
                    template=template_image,
                    threshold=self.settings['threshold']
                )

                self.icon_bindings[name] = binding

                logger.info(f"已添加技能绑定: {binding.text} -> {hotkey}")
                return binding

            except Exception as e:
                logger.error(f"添加技能绑定失败: {e}")
                return None
                
    def remove_icon_binding(self, name: str) -> bool:
        """移除技能图标绑定"""
        with self.lock:
            try:
                # 查找实际的绑定名称
                actual_name = None
                binding_text = None
                for binding_name, binding in self.icon_bindings.items():
                    if binding.name == name:  # 使用显示名称比较
                        actual_name = binding_name
                        binding_text = binding.text
                        break
                        
                if actual_name:
                    # 删除绑定
                    del self.icon_bindings[actual_name]
                    print(f"已删除技能绑定: {binding_text}")  # 使用text
                    return True
                else:
                    print(f"未找到绑定: {name}")
                    return False
                
            except Exception as e:
                print(f"删除绑定时出错: {str(e)}")
                return False

    def save_config(self, spec_name):
        """保存配置到指定职业的配置文件"""
        try:
            # 确保配置目录存在
            if not os.path.exists(self.config_dir):
                os.makedirs(self.config_dir)
            
            # 确保模板目录存在
            template_dir = "templates"
            if not os.path.exists(template_dir):
                os.makedirs(template_dir)
            
            # 准备配置数据
            config = {
                'monitor_region': self.monitor_region,
                'settings': self.settings,
                'icon_bindings': {}
            }
            
            # 保存每个绑定的数据和模板
            for name, binding in self.icon_bindings.items():
                # 保存模板图像
                template_path = os.path.join(template_dir, f"{spec_name}_{name}.png")
                cv2.imwrite(template_path, binding.template)
                print(f"保存模板到: {template_path}")
                
                # 保存绑定数据，包括text属性
                config['icon_bindings'][name] = {
                    'hotkey': binding.hotkey,
                    'template_path': template_path,
                    'text': binding.text
                }
                print(f"保存技能绑定: {binding.text} -> {binding.hotkey}")  # 使用text
                
            # 保存配置文件
            config_path = os.path.join(self.config_dir, f"{spec_name}.json")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"配置已保存到: {config_path}")
            return True
            
        except Exception as e:
            print(f"保存配置时出错: {str(e)}")
            return False
            
    def load_config(self, spec_name):
        """加载指定职业的配置文件"""
        try:
            # 确定配置文件路径
            config_path = os.path.join(self.config_dir, f"{spec_name}.json")
            if not os.path.exists(config_path):
                print(f"配置文件不存在: {config_path}")
                return False
            
            print(f"正在加载配置文件: {config_path}")
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 加载监控区域
            self.monitor_region = config.get('monitor_region')
            print(f"加载监控区域: {self.monitor_region}")
            
            # 加载设置
            saved_settings = config.get('settings', {})
            self.settings.update(saved_settings)
            print(f"加载设置: {self.settings}")
            
            # 清除现有绑定
            self.icon_bindings.clear()
            
            # 加载图标绑定
            bindings_data = config.get('icon_bindings', {})
            success_count = 0
            
            for name, binding_data in bindings_data.items():
                try:
                    # 构建模板文件路径
                    template_path = os.path.join("templates", f"{spec_name}_{name}.png")
                    print(f"尝试加载模板: {template_path}")
                    
                    if os.path.exists(template_path):
                        template = cv2.imread(template_path)
                        if template is not None:
                            # 创建新的绑定，包含text属性
                            binding = IconBinding(
                                name=name,
                                hotkey=binding_data['hotkey'],
                                template=template,
                                text=binding_data.get('text', name),  # 加载text属性，如果没有则使用name
                                threshold=self.settings['threshold']
                            )
                            self.icon_bindings[name] = binding
                            success_count += 1
                            print(f"成功加载技能绑定: {binding.text} ({name}) -> {binding_data['hotkey']}")
                        else:
                            print(f"无法读取模板图像: {template_path}")
                    else:
                        print(f"模板文件不存在: {template_path}")
                        
                except Exception as e:
                    print(f"加载绑定 {name} 时出错: {str(e)}")
                    continue
                
            print(f"成功加载 {success_count}/{len(bindings_data)} 个技能绑定")
            return success_count > 0
            
        except Exception as e:
            print(f"加载配置时出错: {str(e)}")
            return False

    def update_settings(self, new_settings: dict):
        """更新用户设置"""
        with self.lock:
            self.settings.update(new_settings)
            self.save_config()

    def set_monitor_region(self, x1: int, y1: int, x2: int, y2: int):
        """设置Hekili建议技能图标的监控区域"""
        with self.lock:
            self.monitor_region = (x1, y1, x2-x1, y2-y1)
            self.save_config()
        
    def find_icon_in_region(self, icon: IconBinding, region_img: np.ndarray) -> bool:
        """在指定区域内查找技能图标"""
        result = cv2.matchTemplate(region_img, icon.template, cv2.TM_CCOEFF_NORMED)
        _, max_val, _, _ = cv2.minMaxLoc(result)
        return max_val >= self.settings['threshold']  # 使用配置的匹配阈值
        
    def cast_skill(self, binding: IconBinding) -> bool:
        """
        释放技能

        Args:
            binding: 技能绑定对象

        Returns:
            bool: 是否成功释放技能
        """
        current_time = time.time()

        # 检查冷却时间
        if current_time - binding.last_cast < binding.cooldown:
            return False

        try:
            # 验证按键
            if not binding.hotkey:
                logger.error(f"技能 {binding.text} 的按键为空")
                return False

            # 模拟按键
            keyboard.press(binding.hotkey)
            time.sleep(self.settings['key_press_delay'])
            keyboard.release(binding.hotkey)

            # 更新状态
            binding.last_cast = current_time
            binding.update_stats(self.last_match_value)

            # 记录日志
            logger.info(f"释放技能: {binding.text} -> {binding.hotkey}")
            self.update_status(f"释放技能 [{binding.text}] - 按键: {binding.hotkey}")

            return True

        except Exception as e:
            error_msg = f"按键模拟失败 [{binding.text}]: {str(e)}"
            logger.error(error_msg)
            self.update_status(error_msg)
            return False

    # 图像处理方法已移至 ImageProcessor 类

    def find_icon_in_region_with_value(self, binding: IconBinding, region_cv: np.ndarray) -> Tuple[float, int]:
        """在区域中查找图标并返回匹配值"""
        try:
            # 验证输入
            if not self.image_processor.validate_image(region_cv) or not self.image_processor.validate_image(binding.template):
                logger.warning("输入图像无效")
                return 0.0, 1000

            # 使用优化的图像处理器进行匹配
            max_similarity, min_hamming = self.image_processor.find_template_in_image(
                binding.template, region_cv, binding.threshold
            )

            # 保存匹配值
            self.last_match_value = max_similarity

            # 记录匹配结果
            if max_similarity >= binding.threshold:
                logger.info(f"找到技能: {binding.text}, 匹配度: {max_similarity:.2%}")

            return max_similarity, min_hamming

        except Exception as e:
            logger.error(f"查找图标时出错: {e}")
            return 0.0, 1000

    def process_frame(self):
        """处理当前帧,检查Hekili建议区域并释放技能"""
        if not self.monitor_region or not self.enabled:
            return
            
        try:
            with self.lock:
                region = self.monitor_region
                bindings = list(self.icon_bindings.values())
            
            # 截取Hekili建议区域
            screenshot = pyautogui.screenshot(region=region)
            region_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # 检查每个已绑定的图标
            found_icon = False
            for binding in bindings:
                match_value, hamming = self.find_icon_in_region_with_value(binding, region_cv)
                if match_value >= self.settings['threshold']:
                    self.update_status(
                        f"发现技能图标 [{binding.name}] - 相似度: {match_value:.1%} (汉明距离: {hamming})"
                    )
                    self.cast_skill(binding)
                    found_icon = True
                    break
                else:
                    # 显示最接近的匹配结果
                    self.update_status(
                        f"技能 [{binding.name}] - 最大相似度: {match_value:.1%} (汉明距离: {hamming})"
                    )
                    
            if not found_icon:
                self.update_status("监控中... 未发现匹配的技能图标")
                    
        except Exception as e:
            error_msg = f"处理帧时出错: {str(e)}"
            self.update_status(error_msg)
            print(error_msg)
            
    def capture_icon_template(self, x1: int, y1: int, x2: int, y2: int) -> Optional[np.ndarray]:
        """
        捕获技能图标模板

        Args:
            x1, y1, x2, y2: 捕获区域坐标

        Returns:
            捕获的图像或None
        """
        try:
            # 验证坐标
            if x2 <= x1 or y2 <= y1:
                logger.error("无效的捕获区域坐标")
                return None

            # 使用图像处理器捕获屏幕
            region = (x1, y1, x2-x1, y2-y1)
            template = self.image_processor.capture_screen_region(region)

            if template is not None:
                # 预处理模板以提高匹配精度
                processed_template = self.image_processor.preprocess_template(template)
                logger.info(f"成功捕获模板，区域: {region}")
                return processed_template
            else:
                logger.error("捕获模板失败")
                return None

        except Exception as e:
            logger.error(f"捕获图标模板时出错: {e}")
            return None

    def set_status_callback(self, callback):
        """设置状态更新回调函数"""
        self.status_callback = callback
        
    def update_status(self, status: str):
        """更新状态信息"""
        self.last_status = status
        if self.status_callback:
            self.status_callback(status)
            
    def start_key_handler(self):
        """启动按键处理线程"""
        def key_handler():
            while self.enabled:
                try:
                    event_type, data = self.event_queue.get(timeout=0.1)
                    if event_type == 'press_key':
                        keyboard.press_and_release(data)
                except:
                    continue

        thread = threading.Thread(target=key_handler, daemon=True)
        thread.start()
        self.update_status("按键处理线程已启动")
        return thread

    def toggle_enabled(self):
        """切换启用状态"""
        self.enabled = not self.enabled
        if self.enabled:
            self.update_status("自动施法已开启")
        else:
            self.update_status("自动施法已关闭")
        return self.enabled 

    def clear_bindings(self):
        """清除所有图标绑定"""
        with self.lock:
            self.icon_bindings.clear() 

    def delete_config(self, spec_name):
        """删除指定职业的配置及其相关文件"""
        try:
            # 删除配置文件
            config_path = os.path.join(self.config_dir, f"{spec_name}.json")
            if os.path.exists(config_path):
                os.remove(config_path)
                print(f"已删除配置文件: {config_path}")
            
            # 删除相关的模板文件
            template_dir = "templates"
            if os.path.exists(template_dir):
                # 删除该职业的所有模板文件
                for file in os.listdir(template_dir):
                    if file.startswith(f"{spec_name}_"):
                        template_path = os.path.join(template_dir, file)
                        os.remove(template_path)
                        print(f"已删除模板文件: {template_path}")
                    
            return True
        except Exception as e:
            print(f"删除配置时出错: {str(e)}")
            return False 