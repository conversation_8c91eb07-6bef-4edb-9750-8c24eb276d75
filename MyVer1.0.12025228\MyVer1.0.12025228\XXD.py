import sys
import locale
import json
import os
import re
import logging

# 设置默认编码为UTF-8
if sys.platform.startswith('win'):
    # Windows系统
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')
else:
    # 其他系统
    if sys.stdout.encoding != 'UTF-8':
        sys.stdout = open(sys.stdout.fileno(), mode='w', encoding='utf-8', buffering=1)
    if sys.stderr.encoding != 'UTF-8':
        sys.stderr = open(sys.stderr.fileno(), mode='w', encoding='utf-8', buffering=1)

# 设置locale
try:
    locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8' if sys.platform != 'win32' else 'Chinese')
except locale.Error:
    # 如果设置失败，使用默认locale
    pass

import customtkinter as ctk
import cv2
import numpy as np
import pyautogui
import keyboard
import threading
import time
from PIL import Image, ImageTk
from skill_processor import HekiliProcessor, IconBinding
from pynput import keyboard as kb
from pynput import mouse
import tkinter as tk

# 导入新的模块
from config_manager import ConfigManager, AppSettings, WindowConfig
from ui_components import StatusBar, ControlPanel, SpecSelector, SettingsDialog, ConfirmDialog
from error_handler import ErrorHandler, safe_execute, monitor_performance, performance_monitor

# 设置日志
logger = logging.getLogger(__name__)

class RegionSelector:
    def __init__(self, callback, size=50):
        self.callback = callback
        self.size = size
        self.overlay = None
        self.current_pos = None
        self.is_adjusting = False
        
    def start(self):
        """开始区域选择"""
        # 在新线程中启动选择器
        thread = threading.Thread(target=self._run_selector)
        thread.daemon = True
        thread.start()
        
    def _run_selector(self):
        """在新线程中运行选择器"""
        # 创建全屏透明窗口
        self.overlay = tk.Tk()
        self.overlay.attributes('-alpha', 0.3)
        self.overlay.attributes('-fullscreen', True)
        self.overlay.attributes('-topmost', True)
        
        # 创建画布
        self.canvas = tk.Canvas(self.overlay, highlightthickness=0)
        self.canvas.pack(fill='both', expand=True)
        
        # 绑定鼠标事件
        self.canvas.bind('<Motion>', self.on_mouse_move)
        self.canvas.bind('<Button-1>', self.on_mouse_click)
        
        # 绑定键盘事件用于微调
        self.overlay.bind('<Left>', lambda e: self.adjust_position(-1, 0))
        self.overlay.bind('<Right>', lambda e: self.adjust_position(1, 0))
        self.overlay.bind('<Up>', lambda e: self.adjust_position(0, -1))
        self.overlay.bind('<Down>', lambda e: self.adjust_position(0, 1))
        self.overlay.bind('<Return>', lambda e: self.confirm_selection())  # 回车确认
        self.overlay.bind('<Escape>', lambda e: self.cancel())  # ESC取消
        
        # 添加提示文本
        self.guide_text_id = self.canvas.create_text(
            self.overlay.winfo_screenwidth() // 2,
            50,
            text="1. 移动鼠标到技能图标附近\n2. 点击鼠标左键进入微调模式\n3. 使用方向键微调位置\n4. 回车确认选择，ESC取消",
            fill="white",
            font=("Arial", 14)
        )
        
        # 添加预览窗口
        self.preview_frame = tk.Frame(self.overlay, bg='black')
        self.preview_frame.place(x=10, y=100)
        self.preview_label = tk.Label(self.preview_frame)
        self.preview_label.pack(padx=5, pady=5)
        
        self.overlay.mainloop()
        
    def on_mouse_move(self, event):
        """鼠标移动时更新选择框"""
        if self.is_adjusting:
            return
            
        x, y = event.x, event.y
        self.current_pos = (x, y)
        self.update_selection_box(x, y)
        self.update_preview()
        
    def on_mouse_click(self, event):
        """鼠标点击时进入微调模式"""
        if not self.is_adjusting:
            self.is_adjusting = True
            x, y = event.x, event.y
            self.current_pos = (x, y)
            self.update_selection_box(x, y)
            self.update_preview()
            
            # 更新提示文本
            self.canvas.itemconfig(
                self.guide_text_id,
                text="使用方向键微调位置\n按住Shift+方向键进行精确调整\n回车确认选择,ESC取消"
            )
            
            # 绑定Shift+方向键进行精确调整
            self.overlay.bind('<Shift-Left>', lambda e: self.adjust_position(-1, 0))
            self.overlay.bind('<Shift-Right>', lambda e: self.adjust_position(1, 0))
            self.overlay.bind('<Shift-Up>', lambda e: self.adjust_position(0, -1))
            self.overlay.bind('<Shift-Down>', lambda e: self.adjust_position(0, 1))
            
    def adjust_position(self, dx, dy):
        """微调选择框位置"""
        if not self.is_adjusting or not self.current_pos:
            return
            
        x, y = self.current_pos
        # 检查是否按下Shift键
        if self.overlay.focus_get() and self.overlay.focus_get().winfo_toplevel() == self.overlay:
            state = self.overlay.focus_get().winfo_toplevel().winfo_children()[0].winfo_toplevel().state()
            if state & 0x0001:  # Shift键被按下
                dx = dx * 0.2  # 精确调整
                dy = dy * 0.2
                
        x += dx
        y += dy
        self.current_pos = (int(x), int(y))
        self.update_selection_box(x, y)
        self.update_preview()
        
    def update_selection_box(self, x, y):
        """更新选择框显示"""
        half_size = self.size // 2
        x1 = x - half_size
        y1 = y - half_size
        x2 = x + half_size
        y2 = y + half_size
        
        # 更新选择框
        if hasattr(self, 'rect_id') and self.rect_id:
            self.canvas.delete(self.rect_id)
        self.rect_id = self.canvas.create_rectangle(
            x1, y1, x2, y2,
            outline='red',
            width=2
        )
        
        # 更新十字线
        if hasattr(self, 'crosshair'):
            for line_id in self.crosshair:
                self.canvas.delete(line_id)
        self.crosshair = [
            self.canvas.create_line(x, y1-10, x, y1, fill='red', width=2),  # 上
            self.canvas.create_line(x, y2, x, y2+10, fill='red', width=2),  # 下
            self.canvas.create_line(x1-10, y, x1, y, fill='red', width=2),  # 左
            self.canvas.create_line(x2, y, x2+10, y, fill='red', width=2)   # 右
        ]
        
        # 添加中心点
        if hasattr(self, 'center_point') and self.center_point:
            self.canvas.delete(self.center_point)
        self.center_point = self.canvas.create_oval(
            x-2, y-2, x+2, y+2,
            fill='red',
            outline='white'
        )
        
    def update_preview(self):
        """更新预览图像"""
        if not self.current_pos:
            return
            
        try:
            x, y = self.current_pos
            half_size = self.size // 2
            # 截取当前区域的截图
            screenshot = pyautogui.screenshot(region=(
                x - half_size,
                y - half_size,
                self.size,
                self.size
            ))
            
            # 放大显示
            preview_size = self.size * 4  # 放大4倍以便更清晰地查看
            screenshot = screenshot.resize((preview_size, preview_size), Image.LANCZOS)
            
            # 在预览图像上添加网格线
            img_draw = ImageTk.PhotoImage(screenshot)
            self.preview_label.configure(image=img_draw)
            self.preview_label.image = img_draw
            
            # 更新坐标信息
            if hasattr(self, 'coord_label'):
                self.coord_label.destroy()
            self.coord_label = tk.Label(
                self.preview_frame,
                text=f"坐标: ({x}, {y})",
                bg='black',
                fg='white'
            )
            self.coord_label.pack()
            
        except Exception as e:
            print(f"更新预览时出错: {str(e)}")
            
    def confirm_selection(self):
        """确认选择"""
        if not self.current_pos:
            return
            
        x, y = self.current_pos
        half_size = self.size // 2
        x1 = x - half_size
        y1 = y - half_size
        x2 = x + half_size
        y2 = y + half_size
        
        self.overlay.destroy()
        if self.callback:
            self.callback(x1, y1, x2, y2)
            
    def cancel(self):
        """取消选择"""
        self.overlay.destroy()
        if self.callback:
            self.callback(None, None, None, None)

class WoWSkillAssistant:
    """魔兽世界技能助手主类"""

    def __init__(self):
        # 初始化错误处理器
        self.error_handler = ErrorHandler(self._show_error_dialog)

        try:
            self._initialize_app()
        except Exception as e:
            self.error_handler.handle_error(e, "应用程序初始化")
            sys.exit(1)

    @monitor_performance("app_initialization")
    def _initialize_app(self):
        """初始化应用程序"""
        logger.info("开始初始化应用程序...")

        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("孟子 - 加载中...")

        # 初始化配置管理器
        self.config_manager = ConfigManager()

        # 加载历史配置
        logger.info("加载历史配置...")
        self.last_config = self.config_manager.load_last_config()
        logger.info(f"历史配置加载完成: {bool(self.last_config)}")

        # 初始化处理器
        self.processor = HekiliProcessor()

        # 恢复设置
        if "settings" in self.last_config:
            self.processor.settings.update(self.last_config["settings"])
            logger.info("已恢复用户设置")

        # 恢复监控区域
        if "monitor_region" in self.last_config:
            self.processor.monitor_region = self.last_config["monitor_region"]
            logger.info("已恢复监控区域")

        # 初始化UI相关属性（必须在UI操作之前）
        self._initialize_ui_attributes()

        # 加载可用配置
        self.specs = self.config_manager.get_available_specs()
        logger.info(f"找到 {len(self.specs)} 个配置文件")

        # 加载上次使用的配置
        self.current_spec = self.last_config.get("last_spec", "")
        self._load_current_spec()

        # 完成初始化
        self._complete_initialization()

        logger.info("应用程序初始化完成")

    def _initialize_ui_attributes(self):
        """初始化UI相关属性"""
        self.running = False
        self.recent_keys = []
        self.max_recent_keys = 6
        self.last_ui_update = 0
        self.ui_update_interval = 1.0
        self.pending_update = False

        # 设置状态回调
        self.processor.set_status_callback(self.update_status_display)

    def _load_current_spec(self):
        """加载当前配置"""
        if self.current_spec and self.current_spec in self.specs:
            logger.info(f"加载上次使用的配置: {self.current_spec}")
            if not self.processor.load_config(spec_name=self.current_spec):
                logger.warning(f"加载配置失败: {self.current_spec}")
                self.current_spec = self.specs[0] if self.specs else ""
                if self.current_spec:
                    self.processor.load_config(spec_name=self.current_spec)
        elif self.specs:
            self.current_spec = self.specs[0]
            self.processor.load_config(spec_name=self.current_spec)
            logger.info(f"使用默认配置: {self.current_spec}")
        else:
            self.current_spec = ""
            logger.info("没有可用的配置文件")

    def _complete_initialization(self):
        """完成初始化过程"""
        # 设置窗口
        self._setup_window()

        # 设置UI和热键
        self.setup_ui()
        self.setup_hotkeys()

        # 更新UI显示
        self._update_initial_ui()

        # 绑定事件
        self._bind_events()

        # 启动自动保存
        self.start_auto_save()

    @safe_execute(ErrorHandler(), "窗口设置")
    def _setup_window(self):
        """设置窗口属性和位置"""
        # 设置主题
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        # 恢复窗口位置
        window_config = self.last_config.get("window", {})
        if window_config:
            try:
                config = WindowConfig(**window_config)
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()

                if config.is_valid_position(screen_width, screen_height):
                    geometry = f"{config.width}x{config.height}+{config.x}+{config.y}"
                    logger.info(f"恢复窗口位置: {geometry}")
                    self.root.geometry(geometry)
                    self.root.update_idletasks()
                else:
                    self._set_default_window_position()
            except Exception as e:
                logger.error(f"恢复窗口位置失败: {e}")
                self._set_default_window_position()
        else:
            self._set_default_window_position()

        # 设置窗口属性
        self.root.attributes('-topmost', True)
        if sys.platform.startswith('win'):
            self.root.attributes('-toolwindow', True)
        else:
            self.root.resizable(False, False)

    def _update_initial_ui(self):
        """更新初始UI显示"""
        try:
            self.update_binding_list()
            self.update_region_info()

            if self.current_spec:
                if hasattr(self, 'status_bar'):
                    self.status_bar.set_status(f"已加载配置: {self.current_spec}")
                if hasattr(self, 'spec_selector') and hasattr(self.spec_selector, 'spec_var'):
                    self.spec_selector.spec_var.set(self.current_spec)
                self.root.title(f"孟子 - {self.current_spec[-6:]}")
            else:
                if hasattr(self, 'status_bar'):
                    self.status_bar.set_status("请创建新的职业配置")
                if hasattr(self, 'spec_selector') and hasattr(self.spec_selector, 'spec_var'):
                    self.spec_selector.spec_var.set("请创建配置")
                self.root.title("孟子 - 未选择配置")

            self.update_title()
        except Exception as e:
            logger.error(f"更新初始UI时出错: {e}")

    def _bind_events(self):
        """绑定事件"""
        self.root.protocol("WM_DELETE_WINDOW", self.quit_app)
        self.root.bind("<Configure>", self.on_window_configure)
        
    def _set_default_window_position(self):
        """设置默认窗口位置"""
        window_width = 145
        window_height = 50
        x = (self.root.winfo_screenwidth() - window_width) // 2
        y = 30
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.update_idletasks()
        
    @monitor_performance("ui_setup")
    def setup_ui(self):
        """设置用户界面"""
        logger.info("开始设置UI...")

        # 创建主框架
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 创建控制面板回调
        control_callbacks = {
            'toggle_monitoring': self.toggle_monitoring,
            'show_settings': self.show_settings,
            'set_monitor_region': self.set_monitor_region,
            'preview_monitor_region': self.preview_monitor_region,
            'quick_add_binding': self.quick_add_binding
        }

        # 创建控制面板
        self.control_panel = ControlPanel(self.main_frame, control_callbacks)
        self.control_panel.pack(fill="x", padx=5, pady=2)

        # 创建职业选择器回调
        spec_callbacks = {
            'on_spec_change': self.change_spec,
            'create_new_spec': self.create_new_spec,
            'delete_spec': self.delete_spec
        }

        # 创建职业选择器
        self.spec_selector = SpecSelector(
            self.main_frame,
            self.specs,
            self.current_spec,
            spec_callbacks
        )
        self.spec_selector.pack(fill="x", padx=5, pady=2)

        # 绑定列表区域
        self.bindings_frame = ctk.CTkFrame(self.main_frame)
        self.bindings_frame.pack(fill="both", expand=True, padx=5, pady=1)

        # 创建状态栏
        self.status_bar = StatusBar(self.main_frame)
        self.status_bar.pack(fill="x", padx=5, pady=1)

        # 为了兼容性，保留一些旧的引用
        self.start_btn = self.control_panel.start_btn
        self.region_info_label = self.control_panel.region_info_label
        self.status_label = self.status_bar.status_label

        logger.info("UI设置完成")

    def _show_error_dialog(self, message: str):
        """显示错误对话框"""
        try:
            import tkinter.messagebox as msgbox
            msgbox.showerror("错误", message)
        except Exception:
            # 如果无法显示对话框，至少记录到日志
            logger.error(f"无法显示错误对话框: {message}")
        
    def setup_hotkeys(self):
        """设置全局快捷键"""
        self.keyboard_listener = kb.Listener(on_press=self.on_key_press)
        self.keyboard_listener.start()
        
    def on_key_press(self, key):
        """处理快捷键"""
        try:
            if hasattr(key, 'char') and key.char == self.processor.settings['monitor_hotkey']:
                self.toggle_monitoring()
            elif key == kb.Key.f9:  # 使用F9作为切换自动添加的热键
                self.toggle_auto_add()
            elif key == kb.Key.f10:
                self.quick_add_binding()
            elif key == kb.Key.f11:
                self.set_monitor_region()
            elif key == kb.Key.f12:
                self.quit_app()
        except Exception as e:
            print(f"处理快捷键时出错: {str(e)}")
            
    def quick_add_binding(self):
        """快速添加技能绑定"""
        self.status_label.configure(text="请按下要绑定的快捷键...")
        
        def on_key(key):
            try:
                if hasattr(key, 'char'):
                    hotkey = key.char
                else:
                    hotkey = key.name
                    
                # 创建技能名称 (使用英文)
                skill_count = len(self.processor.icon_bindings) + 1
                # 找到未使用的技能编号
                while f"skill_{skill_count}" in self.processor.icon_bindings:
                    skill_count += 1
                name = f"skill_{skill_count}"
                
                # 最小化窗口并等待用户选择图标
                self.root.iconify()
                
                def on_icon_selected(x1, y1, x2, y2):
                    def restore_ui():
                        self.root.deiconify()
                        if all(v is not None for v in (x1, y1, x2, y2)):
                            template = self.processor.capture_icon_template(
                                x1, y1, x2, y2
                            )
                            
                            self.processor.add_icon_binding(name, hotkey, template)
                            self.update_binding_list()
                            self.status_label.configure(text=f"已添加绑定: {name} -> {hotkey}")
                        else:
                            self.status_label.configure(text="添加绑定已取消")
                    
                    # 在主线程中更新UI
                    self.root.after(0, restore_ui)
                
                # 使用相同的选择器来选择技能图标
                selector = RegionSelector(on_icon_selected, size=48)
                selector.start()
                
                return False
            except Exception as e:
                self.status_label.configure(text=f"添加绑定时出错: {str(e)}")
                return False
        
        # 在新线程中监听按键
        key_thread = threading.Thread(target=lambda: kb.Listener(on_press=on_key).start())
        key_thread.daemon = True
        key_thread.start()
        
    def set_monitor_region(self):
        """设置监控区域"""
        def on_region_selected(x1, y1, x2, y2):
            if None in (x1, y1, x2, y2):
                self.status_label.configure(text="已取消选择区域")
                return
            
            try:
                # 设置监控区域
                self.processor.monitor_region = (x1, y1, x2-x1, y2-y1)
                
                # 确保有当前配置并保存
                if self.current_spec:
                    # 保存到当前配置文件
                    if self.processor.save_config(spec_name=self.current_spec):
                        # 更新UI显示
                        self.update_region_info()
                        self.status_label.configure(text=f"已保存监控区域到配置: {self.current_spec}")
                    else:
                        self.status_label.configure(text="保存监控区域失败")
                else:
                    self.status_label.configure(text="请先创建或选择一个配置")
                    
            except Exception as e:
                self.status_label.configure(text=f"设置区域时出错: {str(e)}")
                print(f"设置区域时出错: {str(e)}")  # 添加日志输出
        
        # 停止当前监控
        was_running = self.running
        if was_running:
            self.toggle_monitoring()
        
        # 启动区域选择器
        selector = RegionSelector(callback=on_region_selected)
        selector.start()
        
    def preview_monitor_region(self):
        """预览当前监控区域"""
        if not self.processor.monitor_region:
            self.status_label.configure(text="请先设置监控区域")
            return
            
        x, y, w, h = self.processor.monitor_region
        
        # 创建预览窗口
        preview = tk.Toplevel(self.root)
        preview.title("监控区域预览")
        preview.attributes('-topmost', True)
        
        try:
            # 截取当前区域图像
            screenshot = pyautogui.screenshot(region=(x, y, w, h))
            # 放大显示
            screenshot = screenshot.resize((w*2, h*2), Image.LANCZOS)
            photo = ImageTk.PhotoImage(screenshot)
            
            # 显示图像
            label = tk.Label(preview, image=photo)
            label.image = photo  # 保持引用
            label.pack()
            
            # 添加坐标信息
            info_label = tk.Label(
                preview, 
                text=f"区域: ({x}, {y}) - {w}x{h}",
                font=("Arial", 10)
            )
            info_label.pack(pady=2)
            
            # 添加刷新按钮
            def refresh():
                try:
                    new_shot = pyautogui.screenshot(region=(x, y, w, h))
                    new_shot = new_shot.resize((w*2, h*2), Image.LANCZOS)
                    new_photo = ImageTk.PhotoImage(new_shot)
                    label.configure(image=new_photo)
                    label.image = new_photo
                except Exception as e:
                    print(f"刷新预览时出错: {str(e)}")
            
            refresh_btn = tk.Button(
                preview, 
                text="刷新预览", 
                command=refresh
            )
            refresh_btn.pack(pady=5)
            
            # 添加关闭按钮
            close_btn = tk.Button(
                preview, 
                text="关闭", 
                command=preview.destroy
            )
            close_btn.pack(pady=2)
            
        except Exception as e:
            preview.destroy()
            self.status_label.configure(text=f"预览失败: {str(e)}")
            print(f"预览区域时出错: {str(e)}")
        
    @safe_execute(ErrorHandler(), "更新区域信息")
    def update_region_info(self):
        """更新区域信息显示"""
        try:
            if self.processor.monitor_region:
                x, y, w, h = self.processor.monitor_region
                region_text = f"当前: 【{x}, {y}】"
            else:
                region_text = "未设置监控区域"

            # 使用控制面板的方法更新
            if hasattr(self, 'control_panel'):
                self.control_panel.update_region_info(region_text)

            # 为了兼容性，也更新旧的标签
            if hasattr(self, 'region_info_label'):
                self.region_info_label.configure(text=region_text)

        except Exception as e:
            logger.error(f"更新区域信息失败: {e}")
        
    def update_binding_list(self):
        """高效的增量更新绑定列表"""
        try:
            # 获取当前绑定
            current_bindings = dict(self.processor.icon_bindings)

            # 如果是第一次初始化或者绑定数量变化很大，进行完全重建
            if (not hasattr(self, '_cached_bindings') or
                abs(len(current_bindings) - len(getattr(self, '_cached_bindings', {}))) > 2):
                self._full_rebuild_binding_list(current_bindings)
                return

            # 增量更新：只更新变化的部分
            self._incremental_update_binding_list(current_bindings)

        except Exception as e:
            logger.error(f"更新绑定列表时出错: {e}")
            # 出错时回退到完全重建
            self._full_rebuild_binding_list(self.processor.icon_bindings)

    def _full_rebuild_binding_list(self, bindings_dict):
        """完全重建绑定列表（仅在必要时使用）"""
        logger.info("执行完全重建绑定列表")

        # 清除现有内容
        for widget in self.bindings_frame.winfo_children():
            widget.destroy()

        # 初始化缓存结构
        self._cached_bindings = {}
        self._binding_widgets = {}

        # 创建主容器
        self._create_binding_container()

        # 添加所有绑定
        for binding_name, binding in bindings_dict.items():
            self._add_binding_widget(binding_name, binding)

        # 更新缓存
        self._cached_bindings = dict(bindings_dict)
        logger.info(f"完全重建完成，共 {len(bindings_dict)} 个绑定")

    def _incremental_update_binding_list(self, current_bindings):
        """增量更新绑定列表"""
        cached_bindings = getattr(self, '_cached_bindings', {})

        # 找出需要添加的绑定
        to_add = set(current_bindings.keys()) - set(cached_bindings.keys())
        # 找出需要删除的绑定
        to_remove = set(cached_bindings.keys()) - set(current_bindings.keys())
        # 找出需要更新的绑定
        to_update = set()
        for name in set(current_bindings.keys()) & set(cached_bindings.keys()):
            current = current_bindings[name]
            cached = cached_bindings[name]
            if (current.text != cached.text or
                current.hotkey != cached.hotkey or
                current.get_stats_str() != cached.get_stats_str()):
                to_update.add(name)

        # 执行增量更新
        changes_made = False

        # 删除不存在的绑定
        for name in to_remove:
            self._remove_binding_widget(name)
            changes_made = True

        # 添加新绑定
        for name in to_add:
            self._add_binding_widget(name, current_bindings[name])
            changes_made = True

        # 更新变化的绑定
        for name in to_update:
            self._update_binding_widget(name, current_bindings[name])
            changes_made = True

        if changes_made:
            logger.info(f"增量更新完成: +{len(to_add)} -{len(to_remove)} ~{len(to_update)}")

        # 更新缓存
        self._cached_bindings = dict(current_bindings)

    def _create_binding_container(self):
        """创建绑定容器"""
        # 创建滚动框架
        self.binding_scroll_frame = ctk.CTkScrollableFrame(
            self.bindings_frame,
            orientation="vertical"
        )
        self.binding_scroll_frame.pack(fill="both", expand=True, padx=2, pady=2)

        # 创建表头
        header_frame = ctk.CTkFrame(self.binding_scroll_frame)
        header_frame.pack(fill="x", padx=2, pady=2)

        headers = [
            {"text": "图标", "width": 40},
            {"text": "名称", "width": 80},
            {"text": "按键", "width": 60},
            {"text": "统计", "width": 80},
            {"text": "操作", "width": 60}
        ]

        for header in headers:
            label = ctk.CTkLabel(
                header_frame,
                text=header["text"],
                width=header["width"],
                font=("Arial", 12, "bold")
            )
            label.pack(side="left", padx=2)

    def _add_binding_widget(self, binding_name, binding):
        """添加单个绑定控件"""
        try:
            # 创建绑定行框架
            binding_frame = ctk.CTkFrame(self.binding_scroll_frame, cursor="hand2")
            binding_frame.pack(fill="x", padx=2, pady=1)

            # 创建图标
            icon_frame = ctk.CTkFrame(binding_frame, width=40, height=32)
            icon_frame.pack(side="left", padx=2)
            icon_frame.pack_propagate(False)

            # 转换并显示图标
            try:
                icon_img = cv2.cvtColor(binding.template, cv2.COLOR_BGR2RGB)
                icon_img = Image.fromarray(icon_img)
                icon_img = icon_img.resize((24, 24), Image.Resampling.LANCZOS)
                icon_photo = ImageTk.PhotoImage(icon_img)

                icon_label = tk.Label(
                    icon_frame,
                    image=icon_photo,
                    bg="#2B2B2B" if ctk.get_appearance_mode() == "Dark" else "#DBDBDB",
                    cursor="hand2"
                )
                icon_label.place(relx=0.5, rely=0.5, anchor="center")

                # 保存图片引用防止被垃圾回收
                binding_frame.icon_photo = icon_photo

            except Exception as e:
                logger.error(f"创建图标时出错: {e}")
                # 创建占位符
                placeholder = ctk.CTkLabel(icon_frame, text="?", width=24, height=24, cursor="hand2")
                placeholder.place(relx=0.5, rely=0.5, anchor="center")
                icon_label = placeholder

            # 创建名称标签
            name_label = ctk.CTkLabel(
                binding_frame,
                text=binding.text,
                width=80,
                anchor="w",
                cursor="hand2"
            )
            name_label.pack(side="left", padx=2)

            # 创建按键标签
            hotkey_label = ctk.CTkLabel(
                binding_frame,
                text=binding.hotkey,
                width=60,
                anchor="center",
                cursor="hand2"
            )
            hotkey_label.pack(side="left", padx=2)

            # 创建统计标签
            stats_label = ctk.CTkLabel(
                binding_frame,
                text=binding.get_stats_str(),
                width=80,
                anchor="center",
                cursor="hand2"
            )
            stats_label.pack(side="left", padx=2)

            # 绑定双击事件到所有控件
            widgets_to_bind = [binding_frame, icon_label, name_label, hotkey_label, stats_label]
            for widget in widgets_to_bind:
                widget.bind("<Double-Button-1>", lambda e, b=binding: self.show_edit_menu(e, b))

            # 保存控件引用
            widget_refs = {
                'frame': binding_frame,
                'name_label': name_label,
                'hotkey_label': hotkey_label,
                'stats_label': stats_label,
                'icon_frame': icon_frame,
                'icon_label': icon_label
            }

            if not hasattr(self, '_binding_widgets'):
                self._binding_widgets = {}
            self._binding_widgets[binding_name] = widget_refs

            logger.debug(f"添加绑定控件: {binding.text}")

        except Exception as e:
            logger.error(f"添加绑定控件时出错: {e}")

    def _remove_binding_widget(self, binding_name):
        """删除单个绑定控件"""
        try:
            if hasattr(self, '_binding_widgets') and binding_name in self._binding_widgets:
                widget_refs = self._binding_widgets[binding_name]
                widget_refs['frame'].destroy()
                del self._binding_widgets[binding_name]
                logger.debug(f"删除绑定控件: {binding_name}")
        except Exception as e:
            logger.error(f"删除绑定控件时出错: {e}")

    def _update_binding_widget(self, binding_name, binding):
        """更新单个绑定控件"""
        try:
            if hasattr(self, '_binding_widgets') and binding_name in self._binding_widgets:
                widget_refs = self._binding_widgets[binding_name]

                # 更新文本内容
                widget_refs['name_label'].configure(text=binding.text)
                widget_refs['hotkey_label'].configure(text=binding.hotkey)
                widget_refs['stats_label'].configure(text=binding.get_stats_str())

                # 重新绑定双击事件（确保binding对象是最新的）
                widgets_to_bind = [
                    widget_refs['frame'],
                    widget_refs['icon_label'],
                    widget_refs['name_label'],
                    widget_refs['hotkey_label'],
                    widget_refs['stats_label']
                ]
                for widget in widgets_to_bind:
                    # 先解绑旧事件
                    widget.unbind("<Double-Button-1>")
                    # 绑定新事件
                    widget.bind("<Double-Button-1>", lambda e, b=binding: self.show_edit_menu(e, b))

                logger.debug(f"更新绑定控件: {binding.text}")
        except Exception as e:
            logger.error(f"更新绑定控件时出错: {e}")

    # 移除按钮相关方法，保持原有的双击菜单设计
        
    # 旧的低效更新方法已移除，使用新的增量更新系统

    def show_edit_menu(self, event, binding):
        """显示编辑菜单"""
        menu = tk.Menu(self.root, tearoff=0)
        
        # 编辑名称
        def edit_name():
            try:
                dialog = ctk.CTkInputDialog(
                    text="请输入新的技能名称:",
                    title="编辑技能名称"
                )
                new_name = dialog.get_input()
                if new_name and new_name != binding.text:  # 使用text属性
                    old_text = binding.text
                    binding.text = new_name  # 更新text属性
                    if self.processor.save_config(spec_name=self.current_spec):
                        self.update_binding_list()
                        self.status_bar.set_status(f"已更新技能名称: {old_text} -> {new_name}")
                        print(f"成功保存技能名称修改: {old_text} -> {new_name}")
                    else:
                        self.status_bar.set_status("保存配置失败，请检查配置文件权限")
                        print("保存配置失败，技能名称未更新")
            except Exception as e:
                error_msg = f"编辑技能名称时出错: {str(e)}"
                self.status_bar.set_status(error_msg)
                print(error_msg)
        
        # 编辑按键
        def edit_hotkey():
            self.status_bar.set_status("请按下新的快捷键... (支持单键或ALT+数字组合)")
            
            alt_pressed = False
            
            def on_key(key):
                nonlocal alt_pressed
                try:
                    # 检测ALT键
                    if key == kb.Key.alt_l or key == kb.Key.alt_r:
                        alt_pressed = True
                        return True
                    
                    # 处理按键
                    if hasattr(key, 'char'):
                        if key.char and key.char.isdigit() and alt_pressed:
                            # ALT + 数字组合
                            new_hotkey = f"alt+{key.char}"
                        else:
                            # 普通字符键
                            new_hotkey = key.char
                    else:
                        # 功能键
                        new_hotkey = key.name
                    
                    # 如果是有效的按键，则保存
                    if new_hotkey and new_hotkey != binding.hotkey:
                        old_hotkey = binding.hotkey
                        binding.hotkey = new_hotkey
                        if self.processor.save_config(spec_name=self.current_spec):
                            self.update_binding_list()
                            self.status_bar.set_status(f"已更新快捷键: {old_hotkey} -> {new_hotkey}")
                            print(f"成功保存快捷键修改: {old_hotkey} -> {new_hotkey}")
                        else:
                            self.status_bar.set_status("保存配置失败，请检查配置文件权限")
                            print("保存配置失败，快捷键未更新")
                    return False
                
                except Exception as e:
                    error_msg = f"处理按键时出错: {str(e)}"
                    self.status_bar.set_status(error_msg)
                    print(error_msg)
                    return False
            
            def on_key_release(key):
                nonlocal alt_pressed
                if key == kb.Key.alt_l or key == kb.Key.alt_r:
                    alt_pressed = False
                return True
            
            # 在新线程中监听按键
            listener = kb.Listener(on_press=on_key, on_release=on_key_release)
            listener.start()
        
        menu.add_command(label="编辑名称", command=edit_name)
        menu.add_command(label="编辑按键", command=edit_hotkey)
        menu.add_separator()
        menu.add_command(label="删除", command=lambda: self.remove_binding(binding.name))
        
        try:
            menu.tk_popup(event.x_root, event.y_root)
        finally:
            menu.grab_release()
        
    def remove_binding(self, binding_name):
        """删除绑定并保存配置"""
        if not self.current_spec:
            self.status_bar.set_status("请先选择一个配置")
            return

        self.processor.remove_icon_binding(binding_name)
        if self.processor.save_config(spec_name=self.current_spec):  # 添加配置名称
            self.update_binding_list()
            self.status_bar.set_status(f"已删除绑定: {binding_name}")
        else:
            self.status_bar.set_status("删除失败：无法保存配置")
        
    @safe_execute(ErrorHandler(), "切换监控状态")
    def toggle_monitoring(self):
        """切换监控状态"""
        try:
            # 检查监控区域
            if not self.processor.monitor_region:
                self.status_bar.set_status("请先设置监控区域")
                logger.warning("尝试开始监控但未设置监控区域")
                return

            if not self.running:
                # 开始监控
                self.running = True
                self.processor.enabled = True

                # 更新UI
                self.control_panel.update_start_button(True)

                if not self.processor.icon_bindings:
                    # 如果技能列表为空
                    self.status_bar.set_status("技能列表为空，正在等待添加新技能...")
                    logger.info("开始监控，但技能列表为空")
                else:
                    self.status_bar.set_status("正在监控中...")
                    logger.info("开始监控")

                # 启动监控线程
                self.monitoring_thread = threading.Thread(target=self.monitor_loop, daemon=True)
                self.monitoring_thread.start()

            else:
                # 停止监控
                self.running = False
                self.processor.enabled = False

                # 更新UI
                self.control_panel.update_start_button(False)
                self.status_bar.set_status("已停止")
                logger.info("停止监控")

        except Exception as e:
            self.error_handler.handle_error(e, "切换监控状态")
            
    def monitor_loop(self):
        """监控循环"""
        monitor_thread = threading.Thread(target=self._monitor_worker)
        monitor_thread.daemon = True
        monitor_thread.start()
        
    def _monitor_worker(self):
        """监控工作线程"""
        while self.running:
            try:
                # 调用当前类的process_frame方法，而不是processor的方法
                self.process_frame()
                time.sleep(self.processor.settings['scan_interval'])
            except Exception as e:
                def update_ui():
                    self.status_label.configure(text=f"错误: {str(e)}")
                    self.running = False
                    self.start_btn.configure(
                        text="开始监控 (~)",
                        fg_color=None  # 确保错误时也恢复默认颜色
                    )
                
                # 在主线程中更新UI
                self.root.after(0, update_ui)
                break
                
    def quit_app(self):
        """退出应用"""
        try:
            print("正在保存程序状态...")
            # 停止监控
            self.running = False
            self.processor.enabled = False
            
            # 确保保存当前状态
            self.save_last_config()
            
            # 停止键盘监听
            if hasattr(self, 'keyboard_listener'):
                self.keyboard_listener.stop()
            
            # 退出程序
            self.root.quit()
            
        except Exception as e:
            print(f"程序退出时出错: {str(e)}")
            # 确保程序能够退出
            self.root.quit()
        
    def run(self):
        """运行程序"""
        # 启动主循环
        self.root.mainloop()

    def start_auto_save(self):
        """启动自动保存"""
        self.auto_save()

    def auto_save(self):
        """定期自动保存状态"""
        try:
            self.save_last_config()
            print("自动保存完成")
        except Exception as e:
            print(f"自动保存时出错: {str(e)}")
        finally:
            # 每1分钟保存一次
            self.root.after(60000, self.auto_save)

    def on_window_configure(self, event):
        """窗口位置或大小改变时的回调"""
        if event.widget == self.root:
            # 使用after来延迟保存，避免频繁保存
            if hasattr(self, '_save_timer'):
                self.root.after_cancel(self._save_timer)
            self._save_timer = self.root.after(1000, self.save_last_config)

    def update_status_display(self, status: str):
        """更新状态显示"""
        def _update():
            try:
                # 使用新的状态栏
                if hasattr(self, 'status_bar'):
                    self.status_bar.set_status(status)

                # 为了兼容性，也更新旧的标签
                if hasattr(self, 'status_label'):
                    self.status_label.configure(text=status)

            except Exception as e:
                logger.error(f"更新状态显示失败: {e}")

        # 在主线程中更新UI
        try:
            self.root.after(0, _update)
        except Exception as e:
            logger.error(f"调度状态更新失败: {e}")
        
    @safe_execute(ErrorHandler(), "显示设置")
    def show_settings(self):
        """显示设置窗口"""
        def save_callback(new_settings: dict) -> bool:
            """保存设置的回调函数"""
            try:
                # 更新处理器设置
                self.processor.settings.update(new_settings)

                # 保存到当前配置文件
                if self.current_spec and self.processor.save_config(spec_name=self.current_spec):
                    self.status_bar.set_status(f"已保存设置到配置: {self.current_spec}")
                    logger.info(f"设置已保存到配置: {self.current_spec}")
                    return True
                else:
                    self.status_bar.set_status("保存设置失败：请先选择一个配置")
                    logger.error("保存设置失败：没有当前配置")
                    return False

            except Exception as e:
                error_msg = f"保存设置时出错: {str(e)}"
                self.status_bar.set_status(error_msg)
                logger.error(error_msg)
                return False

        # 创建设置对话框
        SettingsDialog(self.root, self.processor.settings, save_callback)

    def add_recent_key(self, binding_text: str, hotkey: str):
        """添加最近按下的按键到记录"""
        print(f"添加按键记录: {binding_text} -> {hotkey}")
        self.recent_keys.append((binding_text, hotkey))  # 使用text属性
        if len(self.recent_keys) > self.max_recent_keys:
            self.recent_keys.pop(0)  # 移除最旧的记录
        print(f"当前按键记录: {self.recent_keys}")
        
        # 在主线程中更新标题
        self.root.after(0, self.update_title)
        
    def update_title(self):
        """更新窗口标题，显示最近的按键记录和配置名称"""
        try:
            title_parts = []
            
            # 添加配置名称和自动添加状态
            if self.current_spec:
                auto_add_status = "ON" if self.processor.settings.get('auto_add_skills', True) else "OFF"
                title_parts.append(f"孟子 - {self.current_spec[-6:]} [{auto_add_status}]")                          
            else:
                title_parts.append("孟子 - 未选择配置")
            
            # 添加按键记录
            if self.recent_keys:
                recent_three = []
                older_three = []
                
                for text, _ in reversed(self.recent_keys[-6:]):
                    if len(recent_three) < 3:
                        recent_three.append(text)
                    else:
                        older_three.append(text)
                
                key_parts = []
                if recent_three:
                    key_parts.append(f"[{'-'.join(recent_three)}]")
                if older_three:
                    key_parts.append('-'.join(older_three))
                
                if key_parts:
                    title_parts.append(":" + "-".join(key_parts))
            
            self.root.title(" ".join(title_parts))
        except Exception as e:
            print(f"更新标题时出错: {str(e)}")
            self.root.title("孟子")
        
    def cast_skill_and_record(self, binding):
        """释放技能并记录"""
        success = self.processor.cast_skill(binding)
        if success:
            self.add_recent_key(binding.text, binding.hotkey)  # 使用text而不是name

    def process_frame(self):
        """处理当前帧,检查Hekili建议区域并释放技能"""
        if not self.processor.monitor_region or not self.running:
            return
            
        try:
            region = self.processor.monitor_region
            bindings = list(self.processor.icon_bindings.values())
            
            # 截取Hekili建议区域
            screenshot = pyautogui.screenshot(region=region)
            region_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # 检查每个已绑定的图标
            found_icon = False
            max_match = 0
            
            # 如果没有绑定的图标，直接当作新图标处理
            if not bindings and self.current_spec:  # 确保有当前配置
                self.handle_new_icon(region_cv)
                return
            
            for binding in bindings:
                match_value, _ = self.processor.find_icon_in_region_with_value(binding, region_cv)
                if match_value >= self.processor.settings['threshold']:
                    self.cast_skill_and_record(binding)
                    found_icon = True
                    break
                max_match = max(max_match, match_value)
            
            # 调整检测逻辑，添加更多调试信息
            if not found_icon and self.current_spec:  # 确保有当前配置
                print(f"未找到匹配图标，最大匹配值: {max_match}")
                if max_match < 0.77:
                    print("检测到可能的新图标")
                    gray = cv2.cvtColor(region_cv, cv2.COLOR_BGR2GRAY)
                    non_black = np.sum(gray > 30) / gray.size
                    if non_black > 0.1:
                        print(f"非黑色像素比例: {non_black}, 确认为新图标")
                        # 检查是否启用了自动添加
                        if self.processor.settings.get('auto_add_skills', True):
                            self.handle_new_icon(region_cv)
                        else:
                            print("自动添加新技能已禁用")
                    else:
                        print(f"非黑色像素比例过低: {non_black}, 可能是空白区域")
                    
        except Exception as e:
            error_msg = f"错误: {str(e)}"
            print(error_msg)
            self.status_label.configure(text=error_msg)

    def handle_new_icon(self, region_cv):
        """处理发现的新图标"""
        try:
            print("开始处理新图标...")
            # 暂停监控
            was_running = self.running
            self.running = False
            self.processor.enabled = False
            self.start_btn.configure(text="开始监控 (~)")
            
            # 创建预览窗口
            preview = ctk.CTkToplevel(self.root)
            preview.title("发现新技能图标")
            preview.attributes('-topmost', True)
            
            # 计算窗口位置使其居中
            window_width = 300
            window_height = 200
            screen_width = preview.winfo_screenwidth()
            screen_height = preview.winfo_screenheight()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            preview.geometry(f"{window_width}x{window_height}+{x}+{y}")
            
            # 显示图标预览
            icon_img = cv2.cvtColor(region_cv, cv2.COLOR_BGR2RGB)
            icon_img = Image.fromarray(icon_img)
            icon_img = icon_img.resize((48, 48), Image.LANCZOS)
            photo = ImageTk.PhotoImage(icon_img)
            
            # 使用tk.Label而不是ctk.CTkLabel来显示图像
            label = tk.Label(preview, image=photo)
            label.image = photo  # 保持引用
            label.pack(pady=10)
            
            # 生成默认名称
            skill_count = len(self.processor.icon_bindings) + 1
            while f"S-{skill_count}" in self.processor.icon_bindings:
                skill_count += 1
            default_name = f"S-{skill_count}"
            
            # 提示文本
            ctk.CTkLabel(preview, text="发现新技能图标，请按下要绑定的按键").pack(pady=5)
            
            def on_key(key):
                try:
                    if hasattr(key, 'char'):
                        hotkey = key.char
                    else:
                        hotkey = key.name
                        
                    print(f"用户按下按键: {hotkey}")
                    
                    # 确保有当前配置
                    if not self.current_spec:
                        print("错误：没有选择配置文件")
                        self.status_label.configure(text="请先创建或选择一个配置")
                        preview.destroy()
                        return False
                    
                    # 添加新的图标绑定，传入默认名称作为text
                    template = region_cv.copy()
                    self.processor.add_icon_binding(
                        name=default_name,
                        text=default_name,  # 使用相同的默认名称作为显示名称
                        hotkey=hotkey,
                        template_image=template
                    )
                    
                    # 保存到当前配置文件
                    if self.processor.save_config(spec_name=self.current_spec):
                        self.update_binding_list()
                        self.status_label.configure(text=f"已添加新绑定到 {self.current_spec}: {default_name} -> {hotkey}")
                        print(f"成功保存新绑定到配置: {self.current_spec}")
                    else:
                        self.status_label.configure(text="保存配置失败")
                        print("保存配置失败")
                    
                    # 关闭预览窗口
                    preview.destroy()
                    
                    # 如果之前在运行，则恢复监控
                    if was_running:
                        self.toggle_monitoring()
                    
                    return False
                except Exception as e:
                    print(f"处理按键时出错: {str(e)}")
                    self.status_label.configure(text=f"添加绑定时出错: {str(e)}")
                    preview.destroy()
                    return False
            
            # 在新线程中监听按键
            key_thread = threading.Thread(target=lambda: kb.Listener(on_press=on_key).start())
            key_thread.daemon = True
            key_thread.start()
            
        except Exception as e:
            print(f"处理新图标时出错: {str(e)}")
            self.status_label.configure(text=f"处理新图标时出错: {str(e)}")

    def load_specs(self):
        """加载所有可用的职业配置"""
        return self.config_manager.get_available_specs()
        
    @safe_execute(ErrorHandler(), "创建新配置")
    def create_new_spec(self):
        """创建新的职业专精配置"""
        try:
            dialog = ctk.CTkInputDialog(
                text="请输入新职业专精名称:",
                title="新建职业专精"
            )
            new_spec = dialog.get_input()

            if new_spec and new_spec not in self.specs:
                # 保存当前配置
                if self.current_spec:
                    self.processor.save_config(spec_name=self.current_spec)

                # 创建新的配置文件
                self.current_spec = new_spec
                self.specs.append(new_spec)
                self.processor.clear_bindings()  # 清除现有绑定
                self.processor.save_config(spec_name=new_spec)

                # 更新UI组件
                if hasattr(self, 'spec_selector'):
                    self.spec_selector.update_specs(self.specs, new_spec)

                # 更新UI
                self.update_binding_list()
                self.status_bar.set_status(f"已创建新职业专精: {new_spec}")
                self.root.title(f"孟子 - {new_spec[-6:]}")

                logger.info(f"创建新配置: {new_spec}")

        except Exception as e:
            logger.error(f"创建新配置失败: {e}")

    def change_spec(self, spec_name):
        """切换职业专精"""
        print(f"change_spec 被调用: {spec_name}")
        if spec_name == self.current_spec or spec_name == "请创建配置":
            print(f"配置未改变或无效配置名: current={self.current_spec}, new={spec_name}")
            return
        
        try:
            # 清理现有预览图像
            self.clear_preview_images()
            
            # 保存当前配置
            if self.current_spec:
                print(f"保存当前配置: {self.current_spec}")
                self.processor.save_config(spec_name=self.current_spec)
                self.save_last_config()  # 保存历史记录
            
            # 加载新配置
            print(f"准备加载新配置: {spec_name}")
            self.current_spec = spec_name
            if self.processor.load_config(spec_name=spec_name):
                # 更新UI
                self.update_binding_list()
                self.update_region_info()
                self.status_label.configure(text=f"已切换到: {spec_name[-6:]}")
                # 更新窗口标题
                self.root.title(f"孟子 - {spec_name[-6:]}")
                # 立即保存最后使用的配置
                print(f"保存最新配置到历史记录: {spec_name}")
                self.save_last_config()
                print(f"配置切换完成: {spec_name}")
            else:
                print(f"加载配置失败: {spec_name}")
                self.status_label.configure(text=f"加载配置失败: {spec_name[-6:]}")
                self.root.title("孟子 - 配置加载失败")
        except Exception as e:
            print(f"切换配置时出错: {str(e)}")
            self.status_label.configure(text=f"切换配置时出错: {str(e)}")
            self.root.title("孟子 - 错误")

    @safe_execute(ErrorHandler(), "删除配置")
    def delete_spec(self):
        """删除当前职业专精配置"""
        if not self.current_spec:
            self.status_bar.set_status("请先选择要删除的配置")
            return

        def on_confirm(confirmed: bool):
            if confirmed:
                self._perform_delete()

        # 使用新的确认对话框
        ConfirmDialog(
            self.root,
            "确认删除",
            f"确定要删除 {self.current_spec[-6:]} 的配置吗？\n此操作不可恢复！",
            on_confirm
        )

    def _perform_delete(self):
        """执行删除操作"""
        try:
            # 停止监控
            was_running = self.running
            if was_running:
                self.toggle_monitoring()

            # 清理现有预览图像
            self.clear_preview_images()

            spec_to_delete = self.current_spec

            # 使用配置管理器删除配置
            if self.config_manager.delete_spec_config(spec_to_delete):
                # 从列表中移除
                self.specs.remove(spec_to_delete)

                # 更新UI组件
                if self.specs:
                    # 切换到其他配置
                    new_spec = self.specs[0]
                    self.current_spec = new_spec

                    if hasattr(self, 'spec_selector'):
                        self.spec_selector.update_specs(self.specs, new_spec)

                    # 加载新配置
                    self.processor.load_config(spec_name=new_spec)
                    self.update_binding_list()
                    self.update_region_info()
                    self.save_last_config()
                else:
                    # 如果没有配置了
                    self.current_spec = ""
                    if hasattr(self, 'spec_selector'):
                        self.spec_selector.update_specs([], "")
                    self.processor.clear_bindings()
                    self.update_binding_list()

                self.status_bar.set_status(f"已删除配置: {spec_to_delete[-6:]}")

                # 更新窗口标题
                if self.current_spec:
                    self.root.title(f"孟子 - {self.current_spec[-6:]}")
                else:
                    self.root.title("孟子 - 未选择配置")

                logger.info(f"删除配置: {spec_to_delete}")
            else:
                self.status_bar.set_status("删除配置失败")
                logger.error(f"删除配置失败: {spec_to_delete}")

        except Exception as e:
            error_msg = f"删除配置时出错: {str(e)}"
            self.status_bar.set_status(error_msg)
            logger.error(error_msg)

    def clear_preview_images(self):
        """清理所有预览图像"""
        try:
            # 清除绑定框架中的所有内容
            for widget in self.bindings_frame.winfo_children():
                widget.destroy()
        except Exception as e:
            print(f"清理预览图像时出错: {str(e)}")

    # 职业选择器现在由 SpecSelector 组件处理

    @safe_execute(ErrorHandler(), "保存配置")
    def save_last_config(self):
        """保存最后使用的配置"""
        # 这个方法已经在初始化时实现，这里保持兼容性
        pass

    # load_last_config 方法已移至配置管理器

    def toggle_auto_add(self):
        """切换自动添加技能开关"""
        try:
            current = self.processor.settings.get('auto_add_skills', True)
            self.processor.settings['auto_add_skills'] = not current
            
            # 保存设置
            self.save_last_config()
            
            # 更新状态显示
            status = "已开启自动添加技能" if self.processor.settings['auto_add_skills'] else "已关闭自动添加技能"
            self.status_label.configure(text=status)
            print(status)
            
            # 更新标题栏
            self.update_title()
            
        except Exception as e:
            print(f"切换自动添加技能时出错: {str(e)}")

if __name__ == "__main__":
    app = WoWSkillAssistant()
    app.run() 