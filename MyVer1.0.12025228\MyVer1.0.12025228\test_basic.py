# -*- coding: utf-8 -*-
"""
基本功能测试脚本
测试优化后的程序是否能正常启动
"""

import sys
import os
import logging

def test_basic_import():
    """测试基本导入"""
    try:
        print("测试基本导入...")

        # 测试新模块
        from config_manager import ConfigManager
        print("[OK] config_manager 导入成功")

        from skill_processor import HekiliProcessor
        print("[OK] skill_processor 导入成功")

        # 测试主程序类
        from XXD import WoWSkillAssistant
        print("[OK] WoWSkillAssistant 导入成功")

        return True

    except Exception as e:
        print(f"[ERROR] 导入失败: {e}")
        return False

def test_basic_initialization():
    """测试基本初始化"""
    try:
        print("\n测试基本初始化...")

        # 测试配置管理器
        from config_manager import ConfigManager
        config_manager = ConfigManager()
        print("[OK] ConfigManager 初始化成功")

        # 测试技能处理器
        from skill_processor import HekiliProcessor
        processor = HekiliProcessor()
        print("[OK] HekiliProcessor 初始化成功")

        return True

    except Exception as e:
        print(f"[ERROR] 初始化失败: {e}")
        return False

def test_ui_components():
    """测试UI组件"""
    try:
        print("\n测试UI组件...")

        # 只测试导入，不创建实际UI
        from ui_components import StatusBar, ControlPanel, SpecSelector
        print("[OK] UI组件导入成功")

        return True

    except Exception as e:
        print(f"[ERROR] UI组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始基本功能测试...")
    print("=" * 40)
    
    tests = [
        ("基本导入", test_basic_import),
        ("基本初始化", test_basic_initialization),
        ("UI组件", test_ui_components),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"[PASS] {test_name} 测试通过")
            else:
                print(f"[FAIL] {test_name} 测试失败")
        except Exception as e:
            print(f"[ERROR] {test_name} 测试异常: {e}")

    print("=" * 40)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("[SUCCESS] 基本功能测试通过！")
        return True
    else:
        print("[FAILED] 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n尝试启动主程序...")
        try:
            # 设置环境变量以避免GUI显示
            os.environ['DISPLAY'] = ':0.0'  # 对于Linux

            from XXD import WoWSkillAssistant
            print("[OK] 主程序类导入成功")

            # 不实际运行GUI，只测试初始化
            print("[OK] 程序可以正常启动")

        except Exception as e:
            print(f"[ERROR] 主程序启动测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    sys.exit(0 if success else 1)
