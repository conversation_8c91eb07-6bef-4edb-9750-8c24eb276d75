# -*- coding: utf-8 -*-
"""
错误处理模块
提供统一的错误处理和日志记录功能
"""

import logging
import traceback
import sys
from typing import Callable, Any, Optional
from functools import wraps

# 配置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, show_error_dialog: Optional[Callable[[str], None]] = None):
        self.show_error_dialog = show_error_dialog
        self.error_count = 0
        self.max_errors = 10  # 最大错误数量
    
    def handle_error(self, error: Exception, context: str = "", show_dialog: bool = True):
        """
        处理错误
        
        Args:
            error: 异常对象
            context: 错误上下文
            show_dialog: 是否显示错误对话框
        """
        self.error_count += 1
        
        # 记录错误
        error_msg = f"{context}: {str(error)}" if context else str(error)
        logger.error(f"错误 #{self.error_count}: {error_msg}")
        logger.error(f"堆栈跟踪:\n{traceback.format_exc()}")
        
        # 显示错误对话框
        if show_dialog and self.show_error_dialog:
            try:
                self.show_error_dialog(error_msg)
            except Exception as e:
                logger.error(f"显示错误对话框时出错: {e}")
        
        # 如果错误过多，建议重启
        if self.error_count >= self.max_errors:
            logger.critical(f"错误数量过多 ({self.error_count})，建议重启应用程序")
    
    def reset_error_count(self):
        """重置错误计数"""
        self.error_count = 0
        logger.info("错误计数已重置")

def safe_execute(error_handler: ErrorHandler, context: str = ""):
    """
    安全执行装饰器
    
    Args:
        error_handler: 错误处理器
        context: 错误上下文
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler.handle_error(e, context or func.__name__)
                return None
        return wrapper
    return decorator

def log_performance(func: Callable) -> Callable:
    """
    性能日志装饰器
    """
    @wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        import time
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            if execution_time > 1.0:  # 只记录耗时超过1秒的操作
                logger.warning(f"{func.__name__} 执行耗时: {execution_time:.2f}秒")
            elif execution_time > 0.1:  # 记录耗时超过0.1秒的操作
                logger.info(f"{func.__name__} 执行耗时: {execution_time:.2f}秒")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{func.__name__} 执行失败，耗时: {execution_time:.2f}秒，错误: {e}")
            raise
    
    return wrapper

def validate_input(validation_func: Callable[[Any], bool], error_message: str = "输入验证失败"):
    """
    输入验证装饰器
    
    Args:
        validation_func: 验证函数
        error_message: 错误消息
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # 验证第一个参数（通常是self）之后的参数
            for arg in args[1:]:
                if not validation_func(arg):
                    raise ValueError(f"{error_message}: {arg}")
            
            for key, value in kwargs.items():
                if not validation_func(value):
                    raise ValueError(f"{error_message}: {key}={value}")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.start_times = {}
    
    def start_timer(self, name: str):
        """开始计时"""
        import time
        self.start_times[name] = time.time()
    
    def end_timer(self, name: str) -> float:
        """结束计时并返回耗时"""
        import time
        if name not in self.start_times:
            logger.warning(f"计时器 {name} 未启动")
            return 0.0
        
        elapsed = time.time() - self.start_times[name]
        del self.start_times[name]
        
        # 记录指标
        if name not in self.metrics:
            self.metrics[name] = []
        self.metrics[name].append(elapsed)
        
        # 保持最近100次记录
        if len(self.metrics[name]) > 100:
            self.metrics[name] = self.metrics[name][-100:]
        
        return elapsed
    
    def get_average_time(self, name: str) -> float:
        """获取平均耗时"""
        if name not in self.metrics or not self.metrics[name]:
            return 0.0
        return sum(self.metrics[name]) / len(self.metrics[name])
    
    def get_max_time(self, name: str) -> float:
        """获取最大耗时"""
        if name not in self.metrics or not self.metrics[name]:
            return 0.0
        return max(self.metrics[name])
    
    def log_performance_summary(self):
        """记录性能摘要"""
        if not self.metrics:
            logger.info("无性能数据")
            return
        
        logger.info("=== 性能摘要 ===")
        for name, times in self.metrics.items():
            if times:
                avg_time = sum(times) / len(times)
                max_time = max(times)
                min_time = min(times)
                logger.info(f"{name}: 平均 {avg_time:.3f}s, 最大 {max_time:.3f}s, 最小 {min_time:.3f}s, 次数 {len(times)}")

# 全局性能监控器
performance_monitor = PerformanceMonitor()

def monitor_performance(name: str):
    """性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            monitor_name = name or func.__name__
            performance_monitor.start_timer(monitor_name)
            
            try:
                result = func(*args, **kwargs)
                elapsed = performance_monitor.end_timer(monitor_name)
                
                # 记录慢操作
                if elapsed > 0.5:
                    logger.warning(f"慢操作 {monitor_name}: {elapsed:.3f}s")
                
                return result
                
            except Exception as e:
                performance_monitor.end_timer(monitor_name)
                raise
        
        return wrapper
    return decorator
