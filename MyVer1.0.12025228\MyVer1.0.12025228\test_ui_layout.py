# -*- coding: utf-8 -*-
"""
UI布局测试脚本
测试三列布局和编辑反馈功能
"""

import sys
import time

def test_three_column_layout():
    """测试三列布局"""
    print("测试三列布局...")
    
    try:
        from XXD import WoWSkillAssistant
        print("[OK] 主程序导入成功")
        
        app = WoWSkillAssistant()
        print("[OK] 应用实例创建成功")
        
        # 检查三列布局相关方法
        layout_methods = [
            '_create_binding_container',
            '_add_binding_widget',
            '_full_rebuild_binding_list',
            'update_binding_list'
        ]
        
        for method_name in layout_methods:
            if hasattr(app, method_name):
                print(f"[OK] {method_name} 方法存在")
            else:
                print(f"[ERROR] {method_name} 方法不存在")
                return False
        
        # 测试绑定列表更新
        print("\n测试绑定列表更新...")
        initial_count = len(app.processor.icon_bindings)
        print(f"初始绑定数量: {initial_count}")
        
        # 测试完全重建
        start_time = time.time()
        app._full_rebuild_binding_list(app.processor.icon_bindings)
        rebuild_time = time.time() - start_time
        print(f"[OK] 完全重建耗时: {rebuild_time:.3f}秒")
        
        # 检查三列框架
        if hasattr(app, 'binding_frames'):
            frame_count = len(app.binding_frames)
            print(f"[OK] 创建了 {frame_count} 个列框架")
            if frame_count == 3:
                print("[OK] 三列布局正确")
            else:
                print(f"[WARNING] 期望3列，实际{frame_count}列")
        else:
            print("[ERROR] 没有找到binding_frames")
            return False
        
        # 检查控件分布
        if hasattr(app, '_binding_widgets'):
            widget_count = len(app._binding_widgets)
            print(f"[OK] 控件缓存包含 {widget_count} 个控件")
            
            # 检查列分布
            column_distribution = [0, 0, 0]
            for widget_ref in app._binding_widgets.values():
                if 'column_index' in widget_ref:
                    col_idx = widget_ref['column_index']
                    if 0 <= col_idx < 3:
                        column_distribution[col_idx] += 1
            
            print(f"[OK] 列分布: 第1列={column_distribution[0]}, 第2列={column_distribution[1]}, 第3列={column_distribution[2]}")
        
        print("[OK] 三列布局测试完成")
        return True
        
    except Exception as e:
        print(f"[ERROR] 三列布局测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edit_feedback():
    """测试编辑反馈功能"""
    print("\n测试编辑反馈功能...")
    
    try:
        from XXD import WoWSkillAssistant
        app = WoWSkillAssistant()
        
        # 检查编辑相关方法
        edit_methods = [
            'show_edit_menu',
            '_full_rebuild_binding_list',
            '_update_binding_widget'
        ]
        
        for method_name in edit_methods:
            if hasattr(app, method_name):
                print(f"[OK] {method_name} 方法存在")
            else:
                print(f"[ERROR] {method_name} 方法不存在")
                return False
        
        # 检查绑定对象的属性
        bindings = app.processor.icon_bindings
        if bindings:
            first_binding = next(iter(bindings.values()))
            
            if hasattr(first_binding, 'text'):
                print(f"[OK] 绑定有text属性: {first_binding.text}")
            else:
                print("[ERROR] 绑定缺少text属性")
                return False
            
            if hasattr(first_binding, 'hotkey'):
                print(f"[OK] 绑定有hotkey属性: {first_binding.hotkey}")
            else:
                print("[ERROR] 绑定缺少hotkey属性")
                return False
        else:
            print("[INFO] 没有绑定可供测试")
        
        print("[OK] 编辑反馈功能测试完成")
        return True
        
    except Exception as e:
        print(f"[ERROR] 编辑反馈功能测试失败: {e}")
        return False

def test_double_click_menu():
    """测试双击菜单功能"""
    print("\n测试双击菜单功能...")
    
    try:
        from XXD import WoWSkillAssistant
        app = WoWSkillAssistant()
        
        # 检查双击菜单方法
        if hasattr(app, 'show_edit_menu'):
            print("[OK] show_edit_menu 方法存在")
        else:
            print("[ERROR] show_edit_menu 方法不存在")
            return False
        
        # 检查删除方法
        if hasattr(app, 'remove_binding'):
            print("[OK] remove_binding 方法存在")
        else:
            print("[ERROR] remove_binding 方法不存在")
            return False
        
        print("[OK] 双击菜单功能测试完成")
        return True
        
    except Exception as e:
        print(f"[ERROR] 双击菜单功能测试失败: {e}")
        return False

def show_layout_comparison():
    """显示布局对比"""
    print("\n布局对比:")
    print("=" * 50)
    print("修改前的问题:")
    print("- 使用单列滚动布局")
    print("- 有不需要的操作列（编辑、删除按钮）")
    print("- 编辑后UI不更新")
    print()
    print("修改后的改进:")
    print("- 恢复原来的三大列布局")
    print("- 移除操作列，保持双击菜单")
    print("- 编辑后强制重建UI，确保更新")
    print("- 保持熟悉的操作方式")
    print()
    print("布局特点:")
    print("- 三列平均分配技能图标")
    print("- 每列显示: 图标 + 名称 + 按键 + 统计")
    print("- 双击任意部分显示编辑菜单")
    print("- 编辑名称/按键后立即更新显示")

def main():
    """主函数"""
    print("开始UI布局测试...")
    print("=" * 50)
    
    tests = [
        ("三列布局", test_three_column_layout),
        ("编辑反馈", test_edit_feedback),
        ("双击菜单", test_double_click_menu),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"[PASS] {test_name} 测试通过")
            else:
                print(f"[FAIL] {test_name} 测试失败")
        except Exception as e:
            print(f"[ERROR] {test_name} 测试异常: {e}")
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    # 显示布局对比
    show_layout_comparison()
    
    if passed == total:
        print("\n[SUCCESS] UI布局优化测试通过！")
        print("\n功能特点:")
        print("- 恢复了您熟悉的三大列布局")
        print("- 移除了不需要的操作列")
        print("- 保持双击菜单的操作方式")
        print("- 编辑后立即更新UI显示")
        print("- 性能优化的增量更新系统")
        return True
    else:
        print("\n[FAILED] 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
