# -*- coding: utf-8 -*-
"""
配置管理模块
负责处理应用程序的配置文件读写和验证
"""

import json
import os
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class AppSettings:
    """应用程序设置"""
    monitor_hotkey: str = '`'
    threshold: float = 0.90
    scan_interval: float = 0.33
    key_press_delay: float = 0.19
    auto_add_skills: bool = True
    
    def validate(self) -> bool:
        """验证设置的有效性"""
        try:
            if not (0 < self.scan_interval <= 1):
                raise ValueError("扫描间隔必须在0-1秒之间")
            if not (0 < self.threshold <= 1):
                raise ValueError("匹配阈值必须在0-1之间")
            if not (0 < self.key_press_delay <= 1):
                raise ValueError("按键延迟必须在0-1秒之间")
            if not self.monitor_hotkey:
                raise ValueError("监控热键不能为空")
            return True
        except ValueError as e:
            logger.error(f"设置验证失败: {e}")
            return False

@dataclass
class WindowConfig:
    """窗口配置"""
    width: int = 145
    height: int = 50
    x: Optional[int] = None
    y: Optional[int] = None
    
    def is_valid_position(self, screen_width: int, screen_height: int) -> bool:
        """检查窗口位置是否在屏幕范围内"""
        if self.x is None or self.y is None:
            return False
        return (0 <= self.x <= screen_width - self.width and 
                0 <= self.y <= screen_height - self.height)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "configs"):
        self.config_dir = config_dir
        self.history_file = os.path.join(config_dir, "last_config.json")
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs("templates", exist_ok=True)
    
    def load_last_config(self) -> Dict[str, Any]:
        """加载上次的配置"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info("成功加载历史配置")
                return config
        except Exception as e:
            logger.error(f"加载历史配置失败: {e}")
        
        return self._get_default_config()
    
    def save_last_config(self, config: Dict[str, Any]) -> bool:
        """保存当前配置为历史配置"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info("成功保存历史配置")
            return True
        except Exception as e:
            logger.error(f"保存历史配置失败: {e}")
            return False
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "last_spec": "",
            "settings": asdict(AppSettings()),
            "window": asdict(WindowConfig()),
            "monitor_region": None
        }
    
    def load_spec_config(self, spec_name: str) -> Optional[Dict[str, Any]]:
        """加载指定职业的配置"""
        try:
            config_path = os.path.join(self.config_dir, f"{spec_name}.json")
            if not os.path.exists(config_path):
                logger.warning(f"配置文件不存在: {config_path}")
                return None
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 验证配置
            if self._validate_spec_config(config):
                logger.info(f"成功加载配置: {spec_name}")
                return config
            else:
                logger.error(f"配置验证失败: {spec_name}")
                return None
                
        except Exception as e:
            logger.error(f"加载配置失败 {spec_name}: {e}")
            return None
    
    def save_spec_config(self, spec_name: str, config: Dict[str, Any]) -> bool:
        """保存职业配置"""
        try:
            # 验证配置
            if not self._validate_spec_config(config):
                logger.error(f"配置验证失败，无法保存: {spec_name}")
                return False
            
            config_path = os.path.join(self.config_dir, f"{spec_name}.json")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            logger.info(f"成功保存配置: {spec_name}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置失败 {spec_name}: {e}")
            return False
    
    def _validate_spec_config(self, config: Dict[str, Any]) -> bool:
        """验证职业配置的有效性"""
        try:
            # 检查必要的键
            required_keys = ['monitor_region', 'settings', 'icon_bindings']
            for key in required_keys:
                if key not in config:
                    logger.error(f"配置缺少必要键: {key}")
                    return False
            
            # 验证设置
            settings_data = config['settings']
            settings = AppSettings(**settings_data)
            if not settings.validate():
                return False
            
            # 验证监控区域
            monitor_region = config['monitor_region']
            if monitor_region is not None:
                if not isinstance(monitor_region, list) or len(monitor_region) != 4:
                    logger.error("监控区域格式错误")
                    return False
                if not all(isinstance(x, (int, float)) for x in monitor_region):
                    logger.error("监控区域坐标必须是数字")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"配置验证时出错: {e}")
            return False
    
    def get_available_specs(self) -> List[str]:
        """获取所有可用的职业配置"""
        specs = []
        try:
            if os.path.exists(self.config_dir):
                for file in os.listdir(self.config_dir):
                    if file.endswith('.json') and file != 'last_config.json':
                        spec_name = file[:-5]  # 移除 .json 扩展名
                        specs.append(spec_name)
            specs.sort()
            logger.info(f"找到 {len(specs)} 个配置文件")
        except Exception as e:
            logger.error(f"获取配置列表失败: {e}")
        
        return specs
    
    def delete_spec_config(self, spec_name: str) -> bool:
        """删除职业配置及相关文件"""
        try:
            # 删除配置文件
            config_path = os.path.join(self.config_dir, f"{spec_name}.json")
            if os.path.exists(config_path):
                os.remove(config_path)
                logger.info(f"已删除配置文件: {config_path}")
            
            # 删除相关模板文件
            template_dir = "templates"
            if os.path.exists(template_dir):
                deleted_count = 0
                for file in os.listdir(template_dir):
                    if file.startswith(f"{spec_name}_"):
                        template_path = os.path.join(template_dir, file)
                        os.remove(template_path)
                        deleted_count += 1
                logger.info(f"已删除 {deleted_count} 个模板文件")
            
            return True
            
        except Exception as e:
            logger.error(f"删除配置失败 {spec_name}: {e}")
            return False
    
    def backup_config(self, spec_name: str, backup_dir: str = "backups") -> bool:
        """备份配置文件"""
        try:
            os.makedirs(backup_dir, exist_ok=True)
            
            # 备份配置文件
            config_path = os.path.join(self.config_dir, f"{spec_name}.json")
            if os.path.exists(config_path):
                import shutil
                import datetime
                
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = os.path.join(backup_dir, f"{spec_name}_{timestamp}.json")
                shutil.copy2(config_path, backup_path)
                logger.info(f"配置已备份到: {backup_path}")
                return True
            else:
                logger.warning(f"配置文件不存在，无法备份: {config_path}")
                return False
                
        except Exception as e:
            logger.error(f"备份配置失败 {spec_name}: {e}")
            return False
