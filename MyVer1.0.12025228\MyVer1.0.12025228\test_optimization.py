# -*- coding: utf-8 -*-
"""
优化测试脚本
用于验证各个模块的功能是否正常
"""

import sys
import os
import logging
import time

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from config_manager import ConfigManager, AppSettings, WindowConfig
        print("[OK] config_manager 导入成功")
    except Exception as e:
        print(f"[ERROR] config_manager 导入失败: {e}")
        return False

    try:
        from ui_components import StatusBar, ControlPanel, SpecSelector
        print("[OK] ui_components 导入成功")
    except Exception as e:
        print(f"[ERROR] ui_components 导入失败: {e}")
        return False

    try:
        from image_processor import ImageProcessor
        print("[OK] image_processor 导入成功")
    except Exception as e:
        print(f"[ERROR] image_processor 导入失败: {e}")
        return False

    try:
        from error_handler import ErrorHandler, safe_execute, monitor_performance
        print("[OK] error_handler 导入成功")
    except Exception as e:
        print(f"[ERROR] error_handler 导入失败: {e}")
        return False

    try:
        from skill_processor import HekiliProcessor, IconBinding
        print("[OK] skill_processor 导入成功")
    except Exception as e:
        print(f"[ERROR] skill_processor 导入失败: {e}")
        return False
    
    return True

def test_config_manager():
    """测试配置管理器"""
    print("\n测试配置管理器...")
    
    try:
        from config_manager import ConfigManager, AppSettings
        
        # 创建配置管理器
        config_manager = ConfigManager("test_configs")
        
        # 测试默认配置
        default_config = config_manager.load_last_config()
        print(f"✓ 默认配置加载成功: {bool(default_config)}")
        
        # 测试设置验证
        settings = AppSettings()
        if settings.validate():
            print("✓ 设置验证成功")
        else:
            print("✗ 设置验证失败")
            return False
        
        # 测试保存配置
        test_config = {
            "last_spec": "test_spec",
            "settings": {
                "threshold": 0.9,
                "scan_interval": 0.33
            },
            "window": {
                "width": 145,
                "height": 50
            },
            "monitor_region": [100, 100, 50, 50]
        }
        
        if config_manager.save_last_config(test_config):
            print("✓ 配置保存成功")
        else:
            print("✗ 配置保存失败")
            return False
        
        # 清理测试文件
        import shutil
        if os.path.exists("test_configs"):
            shutil.rmtree("test_configs")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        return False

def test_image_processor():
    """测试图像处理器"""
    print("\n测试图像处理器...")
    
    try:
        from image_processor import ImageProcessor
        import numpy as np
        
        processor = ImageProcessor()
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (50, 50, 3), dtype=np.uint8)
        
        # 测试图像验证
        if processor.validate_image(test_image):
            print("✓ 图像验证成功")
        else:
            print("✗ 图像验证失败")
            return False
        
        # 测试哈希计算
        hash_array, hash_str = processor.calculate_image_hash(test_image)
        if hash_array.size > 0 and hash_str:
            print("✓ 图像哈希计算成功")
        else:
            print("✗ 图像哈希计算失败")
            return False
        
        # 测试相似度计算
        similarity, hamming = processor.calculate_hash_similarity(hash_array, hash_array)
        if similarity == 1.0 and hamming == 0:
            print("✓ 相似度计算成功")
        else:
            print("✗ 相似度计算失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 图像处理器测试失败: {e}")
        return False

def test_error_handler():
    """测试错误处理器"""
    print("\n测试错误处理器...")
    
    try:
        from error_handler import ErrorHandler, safe_execute, monitor_performance
        
        # 创建错误处理器
        error_handler = ErrorHandler()
        
        # 测试错误处理
        test_error = ValueError("测试错误")
        error_handler.handle_error(test_error, "测试上下文", show_dialog=False)
        
        if error_handler.error_count == 1:
            print("✓ 错误处理成功")
        else:
            print("✗ 错误处理失败")
            return False
        
        # 测试安全执行装饰器
        @safe_execute(error_handler, "测试函数")
        def test_function():
            raise ValueError("测试异常")
        
        result = test_function()
        if result is None and error_handler.error_count == 2:
            print("✓ 安全执行装饰器成功")
        else:
            print("✗ 安全执行装饰器失败")
            return False
        
        # 测试性能监控装饰器
        @monitor_performance("test_perf")
        def test_perf_function():
            time.sleep(0.01)
            return "success"
        
        result = test_perf_function()
        if result == "success":
            print("✓ 性能监控装饰器成功")
        else:
            print("✗ 性能监控装饰器失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 错误处理器测试失败: {e}")
        return False

def test_skill_processor():
    """测试技能处理器"""
    print("\n测试技能处理器...")
    
    try:
        from skill_processor import HekiliProcessor, IconBinding
        import numpy as np
        
        # 创建处理器
        processor = HekiliProcessor()
        
        # 测试设置
        if processor.settings['threshold'] == 0.9:
            print("✓ 默认设置正确")
        else:
            print("✗ 默认设置错误")
            return False
        
        # 创建测试模板
        test_template = np.random.randint(0, 255, (32, 32, 3), dtype=np.uint8)
        
        # 测试添加绑定
        binding = processor.add_icon_binding("test_skill", "测试技能", "1", test_template)
        if binding and binding.name == "test_skill":
            print("✓ 添加技能绑定成功")
        else:
            print("✗ 添加技能绑定失败")
            return False
        
        # 测试绑定列表
        if len(processor.icon_bindings) == 1:
            print("✓ 绑定列表正确")
        else:
            print("✗ 绑定列表错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 技能处理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始优化测试...")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置管理器", test_config_manager),
        ("图像处理器", test_image_processor),
        ("错误处理器", test_error_handler),
        ("技能处理器", test_skill_processor),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！优化成功！")
        return True
    else:
        print("❌ 部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
